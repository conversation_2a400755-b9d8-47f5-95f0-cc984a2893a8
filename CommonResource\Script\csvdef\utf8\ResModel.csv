﻿资源ID,备注,模型路径,模型文件[如果是程序生成的模型，这里需要配置模型的枚举，否则配置模型的文件名字。0.立方体、1.球体、2.圆柱、3.圆锥、4.三角斜面、5.胶囊体、6.多边形],"资源类型[1.程序生成, 2.obj 3.omod]",名称,图标,一级分类(1.生物 2.),二级分类[1.基础 2.装饰 3.玩法],是否合并SUBMESH,材质类型（按照submesh的顺序设置材质是否是透明材质，0是不透明材质1是透明材质2是遮罩材质 ）
ID,Note,Path,File,ResourceType,Name,Icon,Type,SubType,IfCombineSubmesh,ifTransparent
11000000,立方体,,0,1,立方体,cube,,1,1,0
11000001,球体,,1,1,球体,sphere,,1,0,0
11000002,圆柱体,,2,1,圆柱体,cylinder,,1,0,0
11000003,圆锥,,3,1,圆锥,cone,,1,0,0
11000004,楔形,,4,1,楔形,wedge,,1,0,0
11000005,空心半球,ugcModel,11000005,2,碗,bowl,,2,0,0
11000006,星星,ugcModel,11000006,2,星星,star,,2,0,0
11000007,半圆柱,ugcModel,11000007,2,半圆柱,halfround,,2,0,0
