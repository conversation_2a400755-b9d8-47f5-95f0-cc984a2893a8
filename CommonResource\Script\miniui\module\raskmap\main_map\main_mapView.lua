--声明
local pixelmapView = ClassEx("pixelmapView",ClassList["UIBaseView"])
pixelmapView.__private = {};
local __private = pixelmapView.__private;

--创建
function pixelmapView:Create(param)
	return ClassList["pixelmapView"].new(param)
end

--初始化
function pixelmapView:Init(param)
	self.super:Init(param)
	self.root = param.root
	if not PixelMapInterface:IsEnterSocMap() then
		self.root:SetFrameLevel(11)
		self.root:SetFrameStrata(FS_MEDIUM)
	end

	__private.getHandle(self)
	__private.makeFullScreen(self)
	
	if PixelMapInterface:IsEnterSocMap() then
		self.root:removeChildren()
		self.root:setVisible(false)

		self.soc_pixelmap = __private.initSocMapUI(self)
		self.revivebtn = self.soc_pixelmap:getChildByPath("diedown.revivebtn")
		self.bedlist = self.soc_pixelmap:getChildByPath("diedown.bedlist")
		self.randombtn = self.soc_pixelmap:getChildByPath("diedown.randombtn")
		self.randombtn_title = self.soc_pixelmap:getChildByPath("diedown.randombtn.title")
		self.randombtn_title:setText(GetS(10000754))
		--self.randombtn_title:setText("Random")
	end
end

----------------------------------------private------------------------------------
function __private.getHandle(self)
	self.mm_map_window = self.root:getChild("map_window");
	self.mm_map_follow = self.root:getChild("map_follow");
	self.mm_loader_map = self.root:getChild("loader_map");
	self.mm_forTouch = self.root:getChild("forTouch");
	self.mm_mask = self.mm_loader_map:getChildByPath("map_mask");
	self.mm_window = self.mm_loader_map:getChildByPath("map_mask.window");
	self.mm_right = self.mm_loader_map:getChildByPath("map_mask.right");
	self.mm_top = self.mm_loader_map:getChildByPath("map_mask.top");
	self.mm_bottom = self.mm_loader_map:getChildByPath("map_mask.bottom");
	self.mm_left = self.mm_loader_map:getChildByPath("map_mask.left");
	self.mm_Slider1 = self.root:getChild("Slider1");
	self.mm_position = self.root:getChild("position");
	self.mm_pointNum = self.root:getChild("title_point");
	self.mm_r_title_day = self.root:getChild("r_title_day");
	self.mm_title_time = self.root:getChild("title_time");
	self.mm_Btn_3dMap = self.root:getChild("Btn_3dMap");

	-- self.root:SetFrameLevel(-1)
	-- self.root:SetFrameStrata(FS_NO_DEFINED)
end

function __private.makeFullScreen(self)
	
	self.mm_window:addRelation(self.mm_map_window, Left_Left_RelationType, true);
    self.mm_window:addRelation(self.mm_map_window, Top_Top_RelationType, true);
    self.mm_window:addRelation(self.mm_map_window, Size_RelationType, true);

    self.mm_map_follow:addRelation(self.mm_map_window, Left_Left_RelationType, true);
    self.mm_map_follow:addRelation(self.mm_map_window, Top_Top_RelationType, true);
    self.mm_map_follow:addRelation(self.mm_map_window, Size_RelationType, true);

    -- self.mm_right:addRelation(self.mm_mask, RightExt_Right_RelationType, true);
    -- self.mm_top:addRelation(self.mm_mask, TopExt_Top_RelationType, true);
    -- self.mm_bottom:addRelation(self.mm_mask, RightExt_Right_RelationType, true);
    -- self.mm_bottom:addRelation(self.mm_mask, BottomExt_Bottom_RelationType, true);
	
	local nodes =  {self.root,self.mm_loader_map,self.mm_mask,self.mm_forTouch};
	local scence = GetInst("MiniUISceneMgr"):getCurrentSceneRootNode()
	for k,node in pairs(nodes) do
		node:makeFullScreen();
		node:addRelation(scence, Size_RelationType)
	end
end

function __private.initSocMapUI(self)
	UIPackage:addPackage("miniui/miniworld/adventure")
    local packagename = MiniUIGetPackageNameByPath("miniui/miniworld/adventure")
    local soc_mainmap = UIPackage:createObject(packagename, "soc_mainmap")
	soc_mainmap:setName("soc_mainmap")

	soc_mainmap:setPosition(0,0)
	--接入到主界面
	--local root = GetInst("MiniUIManager"):GetCtrl("playermain").view.root
	local root = GetInst("MiniUISceneMgr"):getCurrentSceneRootNode()

	root:addChild(soc_mainmap)
	soc_mainmap:makeFullScreen()
	soc_mainmap:addRelation(root, Size_RelationType, true)

	self.soc_mainmap = soc_mainmap
	local soc_pixelmap = soc_mainmap:getChild("soc_pixelmap")
	self.soc_pixelmap = soc_pixelmap
	self.close = soc_mainmap:getChild("close")
	self.minimapopen = soc_mainmap:getChild("mobiledir")
	self.minimapinfo = soc_mainmap:getChild("minimapinfo")
	self.mobileminimapinfo = soc_mainmap:getChild("mobileminimapinfo")
	self.showrevive = soc_pixelmap:getController("showrevive")
	self.mapvis = soc_mainmap:getController("mapvis")
	self.mask = soc_pixelmap:getChild("mask")
	self.mobilemask = soc_pixelmap:getChild("mobilemask")
	self.soc_maploader = soc_pixelmap:getChild("soc_maploader")
	self.socposition = soc_pixelmap:getChild("socposition")
	self.position = self.socposition:getChild("position")
	self.taglayer = soc_pixelmap:getChild("taglayer")
	self.event = soc_pixelmap:getChild("event")
	self.tagedit = soc_pixelmap:getChild("tagedit")
	self.mapright = soc_pixelmap:getChild("mapright")
	self.mapright_list = self.mapright:getChild("list")
	self.showedit = soc_pixelmap:getController("showedit")
	self.tagshowlist = soc_pixelmap:getChildByPath("soc_marker_com.tagshowlist")
	self.mapsize = soc_pixelmap:getChild("mapsize")
	self.posbtn = self.mapright:getChild("posbtn")
	self.gridbtn = self.mapright:getChild("gridbtn")
	self.enterbtn = self.tagedit:getChild("enter")
	self.editinput = self.tagedit:getChild("edit")

	return soc_pixelmap
end