--声明
local storageboxModel = Class("storageboxModel",ClassList["UIBaseModel"])

--创建
function storageboxModel:Create(param)
	return ClassList["storageboxModel"].new(param)
end

--初始化
function storageboxModel:Init(param)
	self.super:Init(param)

end

function storageboxModel:SetIncomingParam(param)
	self.data.incomingParam = param
	self.blockid = param.blockid

	MiniLog("storageboxModel:SetIncomingParam", self.blockid)
end

function storageboxModel:GetBlockId()
	return self.data.incomingParam.blockid
end

function storageboxModel:GetBlockName()

	if self.blockid == 10000748 then 
		return GetS(10000748)--玩家的背包
	elseif self.blockid == 10000799 then
		return GetS(10000799)--掉落物的背包
	end

	local blockdef = DefMgr:getBlockDef(self.blockid, false)
	if blockdef then
		return blockdef.Name
	end
	return ""
end
