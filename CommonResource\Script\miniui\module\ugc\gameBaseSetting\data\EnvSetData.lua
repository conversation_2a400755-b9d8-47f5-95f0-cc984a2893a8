
local MapEnvSet = {
    curSkyTemplateIdx = 1,
    curPostProcessTemplateIdx = 1,
    TimePassing = 1.0,
} 

-- 粒子暂时没加
local defParticle = {

}

-- 天空模板参数
local SkyTemplate = {
    [1] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_def",
        Name = GetS(301257),
        TemplateIdx = 1,
        Enable = false,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=36, g=114, b=250},
                    MidColor = {r=255, g=119, b=0},
                    BottomColor = {r=255, g=109, b=0},
                    SkyLightColor = {r=157, g=89, b=72},
                    AmbientColor = {r=11, g=29, b=52},
                },

                Sun = {
                    Color = {r=227, g=159, b=124},
                    Width = 1,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 1,
                },
                
                Star = {
                    Density=0.5,
                },

                Fog = {
                    Enable = false,
                    Color = {r=255, g=109, b=0},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 248, g = 124, b = 6},
                },

                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=0, g=131, b=255},
                    MidColor = {r=165, g=211, b=255},
                    BottomColor = {r=177, g=227, b=255},
                    SkyLightColor = {r=221, g=200, b=156},
                    AmbientColor = {r=40, g=33, b=72},
                },

                Sun = {
                    Color = {r=189, g=197, b=248},
                    Width = 1,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 1,
                },
                
                Star = {
                    Density=0.001,
                },

                Fog = {
                    Enable = false,
                    Color = {r=177, g=227, b=255},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 140, g = 140, b = 140},
                },

                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=39, g=112, b=255},
                    MidColor = {r=165, g=210, b=255},
                    BottomColor = {r=177, g=226, b=255},
                    SkyLightColor = {r=253, g=253, b=224},
                    AmbientColor = {r=26, g=23, b=54},
                },

                Sun = {
                    Color = {r=215, g=216, b=217},
                    Width = 1,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 1,
                },
                
                Star = {
                    Density=0.001,
                },

                Fog = {
                    Enable = false,
                    Color =  {r=177, g=226, b=255},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 134, g = 134, b = 134},
                } ,
                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=39, g=112, b=255},
                    MidColor = {r=165, g=211, b=255},
                    BottomColor = {r=177, g=226, b=255},
                    SkyLightColor = {r=239, g=217, b=175},
                    AmbientColor = {r=18, g=16, b=20},
                },

                Sun = {
                    Color = {r=249, g=199, b=147},
                    Width = 1,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 1,
                },
                
                Star = {
                    Density=0.001,
                },

                Fog = {
                    Enable = false,
                    Color =  {r=177, g=226, b=255},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 140, g = 140, b = 140},
                },
                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [5] = {
                Sky = {
                    TopColor = {r=0, g=93, b=255},
                    MidColor = {r=255, g=163, b=0},
                    BottomColor = {r=254, g=128, b=50},
                    SkyLightColor = {r=218, g=179, b=144},
                    AmbientColor = {r=31, g=28, b=54},
                },

                Sun = {
                    Color = {r=203, g=121, b=99},
                    Width = 1,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 1,
                },
                
                Star = {
                    Density=0.5,
                },

                Fog = {
                    Enable = false,
                    Color = {r=254, g=128, b=50},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 248, g = 124, b = 6},
                },
                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [6] = {
                Sky = {
                    TopColor = {r=0, g=19, b=94},
                    MidColor = {r=19, g=42, b=142},
                    BottomColor = {r=0, g=1, b=36},
                    SkyLightColor = {r=131, g=120, b=171},
                    AmbientColor = {r=33, g=34, b=64},
                },

                Sun = {
                    Color = {r=53, g=70, b=112},
                    Width = 1,
                },

                Moon = {
                    Color = {r=102, g=138, b=231},
                    Width = 1,
                },
                
                Star = {
                    Density=1,
                },

                Fog = {
                    Enable = false,
                    Color = {r=0, g=1, b=36},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 26, g = 26, b = 26},
                },
                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [7] = {
                Sky = {
                    TopColor = {r=0, g=6, b=29},
                    MidColor = {r=19, g=42, b=142},
                    BottomColor = {r=0, g=2, b=41},
                    SkyLightColor = {r=30, g=99, b=166},
                    AmbientColor = {r=19, g=17, b=60},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 1,
                },

                Moon = {
                    Color = {r=127, g=165, b=205},
                    Width = 1,
                },
                
                Star = {
                    Density=1,
                },

                Fog = {
                    Enable = false,
                    Color = {r=0, g=2, b=41},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 21, g = 21, b = 21},
                },
                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [8] = {
                Sky = {
                    TopColor = {r=0, g=19, b=89},
                    MidColor = {r=19, g=42, b=142},
                    BottomColor = {r=0, g=1, b=37},
                    SkyLightColor = {r=68, g=77, b=91},
                    AmbientColor = {r=39, g=37, b=63},
                },

                Sun = {
                    Color = {r=125, g=60, b=244},
                    Width = 1,
                },

                Moon = {
                    Color = {r=129, g=115, b=150},
                    Width = 1,
                },
                
                Star = {
                    Density=1,
                },

                Fog = {
                    Enable = false,
                    Color = {r=0, g=1, b=37},
                    RangeStart = 10,
                    RangeEnd = 32,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
        },
    },
    [2] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_cartoon",
        Name = GetS(301269),
        TemplateIdx = 2,
        SkyTex = {
            idx = 7,
            path = "ugcenv/sky1.png",
        },
        SunTex = {
            idx = 0,
            path = "",
        },
        MoonTex = {
            idx = 0,
            path = "",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 163, g = 236, r = 255, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 178, g = 160, r = 44, },
                    SkyLightColor = { b = 255, g = 207, r = 63, },
                    BottomColor = { b = 170, g = 255, r = 235, },
                    AmbientColor = { b = 122, g = 87, r = 17, },
                    TopColor = { b = 160, g = 99, r = 0, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { b = 0, g = 109, r = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 86, g = 240, r = 255, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { r = 189, g = 197, b = 248, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 188, g = 249, r = 255, },
                    SkyLightColor = { b = 255, g = 241, r = 201, },
                    BottomColor = { b = 201, g = 240, r = 255, },
                    AmbientColor = { b = 130, g = 104, r = 112, },
                    TopColor = { b = 255, g = 250, r = 0, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 177, g = 227, b = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { r = 215, g = 216, b = 217, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 255, g = 237, r = 0, },
                    SkyLightColor = { b = 255, g = 249, r = 214, },
                    BottomColor = { b = 242, g = 252, r = 255, },
                    AmbientColor = { b = 193, g = 193, r = 193, },
                    TopColor = { b = 255, g = 97, r = 0, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 134, g = 134, b = 134, },
                    Density = 24,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { r = 249, g = 199, b = 147, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 255, g = 165, r = 196, },
                    SkyLightColor = { b = 232, g = 144, r = 104, },
                    BottomColor = { b = 255, g = 246, r = 244, },
                    AmbientColor = { b = 175, g = 167, r = 167, },
                    TopColor = { r = 39, g = 112, b = 255, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { r = 203, g = 121, b = 99, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 255, g = 84, r = 212, },
                    SkyLightColor = { b = 132, g = 132, r = 132, },
                    BottomColor = { b = 255, g = 249, r = 254, },
                    AmbientColor = { b = 109, g = 88, r = 99, },
                    TopColor = { r = 0, g = 93, b = 255, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 254, g = 128, b = 50, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 248, g = 124, b = 6, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { r = 53, g = 70, b = 112, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 242, g = 53, r = 132, },
                    SkyLightColor = { b = 132, g = 80, r = 90, },
                    BottomColor = { b = 204, g = 32, r = 118, },
                    AmbientColor = { b = 191, g = 80, r = 96, },
                    TopColor = { b = 127, g = 27, r = 0, },
                },
                Moon = {
                    Color = { r = 102, g = 138, b = 231, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 0, g = 1, b = 36, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 26, g = 26, b = 26, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 191, g = 0, r = 50, },
                    SkyLightColor = { b = 160, g = 59, r = 72, },
                    BottomColor = { b = 94, g = 4, r = 0, },
                    AmbientColor = { b = 165, g = 41, r = 76, },
                    TopColor = { b = 201, g = 30, r = 118, },
                },
                Moon = {
                    Color = { r = 127, g = 165, b = 205, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 0, g = 2, b = 41, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 21, g = 21, b = 21, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { r = 125, g = 60, b = 244, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { r = 19, g = 42, b = 142, },
                    SkyLightColor = { b = 153, g = 59, r = 4, },
                    BottomColor = { b = 160, g = 142, r = 3, },
                    AmbientColor = { b = 109, g = 60, r = 62, },
                    TopColor = { r = 0, g = 19, b = 89, },
                },
                Moon = {
                    Color = { r = 129, g = 115, b = 150, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 0, g = 1, b = 37, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 22, g = 22, b = 22, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=255, a=200},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [3] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_nature",
        Name = GetS(301270),
        TemplateIdx = 3,
        SkyTex = {
            idx = 0,
            path = "",
        },
        SunTex = {
            idx = 0,
            path = "",
        },
        MoonTex = {
            idx = 0,
            path = "",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 160, g = 240, r = 255, },
                    Width = 0.63218392431736,
                },
                Sky = {
                    MidColor = { b = 187, g = 237, r = 236, },
                    SkyLightColor = { b = 124, g = 109, r = 62, },
                    BottomColor = { b = 199, g = 238, r = 249, },
                    AmbientColor = { b = 61, g = 30, r = 6, },
                    TopColor = { b = 124, g = 35, r = 13, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Color = { b = 194, g = 240, r = 249, },
                    Enable = true,
                    RangeEnd = 60,
                    RangeStart = 14,
                },
                Cloud = {
                    Color = { b = 0, g = 161, r = 255, },
                    Speed = 1,
                    Height = 21,
                    Density = 2.1,
                },
                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 98, g = 225, r = 229, },
                    Width = 0.51724135875702,
                },
                Sky = {
                    MidColor = { b = 255, g = 244, r = 234, },
                    SkyLightColor = { r = 221, g = 200, b = 156, },
                    BottomColor = { b = 255, g = 254, r = 226, },
                    AmbientColor = { b = 124, g = 105, r = 56, },
                    TopColor = { b = 183, g = 120, r = 18, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Color = { b = 255, g = 250, r = 226, },
                    Enable = true,
                    RangeEnd = 74,
                    RangeStart = 38,
                },
                Cloud = {
                    Color = { b = 221, g = 221, r = 215, },
                    Speed = 2,
                    Height = 33,
                    Density = 4,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 255, g = 255, r = 255, },
                    Width = 0.63218392431736,
                },
                Sky = {
                    MidColor = { b = 255, g = 249, r = 206, },
                    SkyLightColor = { b = 255, g = 255, r = 255, },
                    BottomColor = { b = 255, g = 243, r = 206, },
                    AmbientColor = { b = 100, g = 173, r = 188, },
                    TopColor = { b = 255, g = 111, r = 28, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Color = { b = 255, g = 243, r = 224, },
                    Enable = true,
                    RangeEnd = 73,
                    RangeStart = 58,
                },
                Cloud = {
                    Color = { b = 255, g = 255, r = 255, },
                    Speed = 1,
                    Height = 97,
                    Density = 0,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { b = 100, g = 189, r = 244, },
                    Width = 0.59770114719868,
                },
                Sky = {
                    MidColor = { b = 196, g = 239, r = 255, },
                    SkyLightColor = { b = 113, g = 175, r = 196, },
                    BottomColor = { b = 216, g = 253, r = 255, },
                    AmbientColor = { b = 122, g = 174, r = 219, },
                    TopColor = { b = 234, g = 97, r = 18, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Color = { b = 198, g = 252, r = 255, },
                    Enable = true,
                    RangeEnd = 85,
                    RangeStart = 71,
                },
                Cloud = {
                    Color = { b = 164, g = 216, r = 215, },
                    Speed = 1,
                    Height = 33,
                    Density = 13,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 81, g = 116, r = 255, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 196, g = 45, r = 138, },
                    SkyLightColor = { b = 63, g = 156, r = 226, },
                    BottomColor = { b = 89, g = 177, r = 249, },
                    AmbientColor = { b = 1, g = 35, r = 58, },
                    TopColor = { b = 255, g = 133, r = 58, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0,
                },
                Fog = {
                    Color = { b = 107, g = 173, r = 255, },
                    Enable = true,
                    RangeEnd = 69,
                    RangeStart = 37,
                },
                Cloud = {
                    Color = { b = 21, g = 119, r = 211, },
                    Speed = 1,
                    Height = 17,
                    Density = 26,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { r = 53, g = 70, b = 112, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 204, g = 128, r = 77, },
                    SkyLightColor = { b = 204, g = 80, r = 67, },
                    BottomColor = { b = 165, g = 82, r = 124, },
                    AmbientColor = { b = 91, g = 50, r = 49, },
                    TopColor = { b = 109, g = 28, r = 5, },
                },
                Moon = {
                    Color = { b = 137, g = 230, r = 237, },
                    Width = 0.45977011322975,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Color = { b = 153, g = 85, r = 113, },
                    Enable = true,
                    RangeEnd = 99,
                    RangeStart = 28,
                },
                Cloud = {
                    Color = { b = 66, g = 66, r = 66, },
                    Speed = 1,
                    Height = 17,
                    Density = 16,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 142, g = 34, r = 4, },
                    SkyLightColor = { b = 150, g = 55, r = 20, },
                    BottomColor = { b = 89, g = 20, r = 23, },
                    AmbientColor = { b = 87, g = 50, r = 20, },
                    TopColor = { b = 58, g = 18, r = 7, },
                },
                Moon = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 0.42528748512268,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Color = { b = 89, g = 20, r = 23, },
                    Enable = true,
                    RangeEnd = 81,
                    RangeStart = 39,
                },
                Cloud = {
                    Color = { b = 73, g = 44, r = 36, },
                    Speed = 1,
                    Height = 17,
                    Density = 0,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { r = 125, g = 60, b = 244, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 178, g = 92, r = 0, },
                    SkyLightColor = { r = 0, g = 77, b = 91, },
                    BottomColor = { b = 86, g = 37, r = 9, },
                    AmbientColor = { r = 39, g = 37, b = 63, },
                    TopColor = { b = 81, g = 18, r = 0, },
                },
                Moon = {
                    Color = { b = 145, g = 145, r = 111, },
                    Width = 0.51724135875702,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Color = { b = 91, g = 36, r = 12, },
                    Enable = true,
                    RangeEnd = 71,
                    RangeStart = 35,
                },
                Cloud = {
                    Color = { r = 22, g = 22, b = 22, },
                    Speed = 1,
                    Height = 11,
                    Density = 13,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=100, b=204, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [4] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_dream",
        Name = GetS(301263),
        TemplateIdx = 4,
        SkyTex = {
        },
        SunTex = {
            idx = 5,
            path = "ugcenv/sun5.png",
        },
        MoonTex = {
            idx = 4,
            path = "ugcenv/moon4.png",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 137, g = 202, r = 255, },
                    Width = 1.229885071516,
                },
                Sky = {
                    MidColor = { b = 226, g = 153, r = 247, },
                    SkyLightColor = { b = 241, g = 173, r = 255, },
                    BottomColor = { b = 235, g = 206, r = 255, },
                    AmbientColor = { b = 55, g = 24, r = 56, },
                    TopColor = { b = 140, g = 50, r = 98, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = true,
                    Color = { b = 233, g = 206, r = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 167, g = 184, r = 242, },
                    Density = 6,
                    Height = 7,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 190, g = 191, r = 247, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 193, g = 249, r = 255, },
                    SkyLightColor = { b = 77, g = 79, r = 155, },
                    BottomColor = { b = 206, g = 232, r = 255, },
                    AmbientColor = { b = 178, g = 202, r = 255, },
                    TopColor = { b = 191, g = 135, r = 255, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 66,
                    Enable = true,
                    Color = { b = 201, g = 231, r = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 135, g = 209, r = 255, },
                    Density = 0,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 255, g = 255, r = 255, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 179, g = 124, r = 255, },
                    SkyLightColor = { b = 247, g = 224, r = 255, },
                    BottomColor = { b = 226, g = 235, r = 255, },
                    AmbientColor = { b = 193, g = 193, r = 201, },
                    TopColor = { b = 255, g = 236, r = 130, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 54,
                    Enable = true,
                    Color = { b = 226, g = 235, r = 255, },
                    RangeStart = 24,
                },
                Cloud = {
                    Color = { b = 235, g = 158, r = 255, },
                    Density = 25,
                    Height = 28,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { b = 15, g = 140, r = 138, },
                    Width = 1.862068772316,
                },
                Sky = {
                    MidColor = { b = 155, g = 221, r = 255, },
                    SkyLightColor = { b = 255, g = 186, r = 251, },
                    BottomColor = { b = 237, g = 178, r = 255, },
                    AmbientColor = { b = 142, g = 171, r = 219, },
                    TopColor = { b = 255, g = 81, r = 217, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 51,
                    Enable = true,
                    Color = { b = 237, g = 178, r = 255, },
                    RangeStart = 23,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 20,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 96, g = 151, r = 247, },
                    Width = 2.1494252979755,
                },
                Sky = {
                    MidColor = { b = 255, g = 142, r = 217, },
                    SkyLightColor = { b = 126, g = 107, r = 219, },
                    BottomColor = { b = 165, g = 182, r = 255, },
                    AmbientColor = { b = 57, g = 81, r = 79, },
                    TopColor = { b = 170, g = 27, r = 103, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 45,
                    Enable = true,
                    Color = { b = 163, g = 181, r = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 248, g = 124, b = 6, },
                    Density = 5,
                    Height = 11,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { r = 53, g = 70, b = 112, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 221, g = 135, r = 216, },
                    SkyLightColor = { b = 191, g = 76, r = 116, },
                    BottomColor = { b = 206, g = 84, r = 176, },
                    AmbientColor = { b = 55, g = 58, r = 102, },
                    TopColor = { b = 132, g = 14, r = 32, },
                },
                Moon = {
                    Color = { b = 102, g = 199, r = 226, },
                    Width = 0.80459773540497,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 48,
                    Enable = true,
                    Color = { b = 201, g = 84, r = 176, },
                    RangeStart = 13,
                },
                Cloud = {
                    Color = { b = 206, g = 206, r = 206, },
                    Density = 22,
                    Height = 6,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 216, g = 43, r = 153, },
                    SkyLightColor = { b = 52, g = 45, r = 76, },
                    BottomColor = { b = 107, g = 26, r = 87, },
                    AmbientColor = { b = 44, g = 63, r = 36, },
                    TopColor = { b = 114, g = 0, r = 59, },
                },
                Moon = {
                    Color = { b = 35, g = 236, r = 255, },
                    Width = 0.40229886770248,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 71,
                    Enable = false,
                    Color = { b = 107, g = 26, r = 87, },
                    RangeStart = 24,
                },
                Cloud = {
                    Color = { b = 40, g = 39, r = 40, },
                    Density = 2,
                    Height = 18,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { r = 125, g = 60, b = 244, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 175, g = 47, r = 124, },
                    SkyLightColor = { b = 183, g = 73, r = 141, },
                    BottomColor = { b = 135, g = 78, r = 129, },
                    AmbientColor = { b = 91, g = 45, r = 64, },
                    TopColor = { b = 119, g = 7, r = 57, },
                },
                Moon = {
                    Color = { b = 158, g = 103, r = 101, },
                    Width = 0.3678160905838,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = true,
                    Color = { b = 142, g = 75, r = 132, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 22, g = 22, r = 22, },
                    Density = 16,
                    Height = 14,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=212, b=255, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [5] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_ice",
        Name = GetS(301264),
        TemplateIdx = 5,
        SkyTex = {
            idx = 0,
            path = "",
        },
        SunTex = {
            idx = 0,
            path = "",
        },
        MoonTex = {
            idx = 0,
            path = "",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 150, g = 133, r = 51, },
                    SkyLightColor = { b = 104, g = 72, r = 54, },
                    BottomColor = { b = 173, g = 162, r = 126, },
                    AmbientColor = { b = 61, g = 44, r = 30, },
                    TopColor = { b = 147, g = 98, r = 65, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { r = 255, g = 109, b = 0, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { b = 102, g = 102, r = 102, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 211, g = 195, r = 114, },
                    SkyLightColor = { b = 239, g = 164, r = 131, },
                    BottomColor = { b = 239, g = 220, r = 155, },
                    AmbientColor = { b = 160, g = 114, r = 83, },
                    TopColor = { b = 183, g = 126, r = 88, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { b = 239, g = 220, r = 155, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 140, g = 140, b = 140, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 221, g = 212, r = 75, },
                    SkyLightColor = { b = 239, g = 154, r = 105, },
                    BottomColor = { b = 255, g = 248, r = 211, },
                    AmbientColor = { b = 234, g = 203, r = 152, },
                    TopColor = { b = 196, g = 109, r = 27, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { b = 255, g = 248, r = 211, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 134, g = 134, b = 134, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 224, g = 194, r = 139, },
                    SkyLightColor = { b = 193, g = 116, r = 81, },
                    BottomColor = { b = 219, g = 211, r = 177, },
                    AmbientColor = { b = 211, g = 137, r = 91, },
                    TopColor = { b = 178, g = 55, r = 55, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 140, g = 140, b = 140, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 175, g = 144, r = 100, },
                    SkyLightColor = { b = 196, g = 167, r = 94, },
                    BottomColor = { b = 160, g = 142, r = 107, },
                    AmbientColor = { b = 132, g = 42, r = 9, },
                    TopColor = { b = 142, g = 67, r = 19, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { r = 254, g = 128, b = 50, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { b = 82, g = 84, r = 86, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 178, g = 88, r = 66, },
                    SkyLightColor = { b = 198, g = 90, r = 65, },
                    BottomColor = { b = 124, g = 83, r = 48, },
                    AmbientColor = { b = 76, g = 47, r = 45, },
                    TopColor = { b = 94, g = 19, r = 0, },
                },
                Moon = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { r = 0, g = 1, b = 36, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 26, g = 26, b = 26, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 163, g = 10, r = 4, },
                    SkyLightColor = { b = 99, g = 55, r = 8, },
                    BottomColor = { b = 181, g = 132, r = 18, },
                    AmbientColor = { b = 34, g = 37, r = 25, },
                    TopColor = { b = 79, g = 17, r = 0, },
                },
                Moon = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { r = 0, g = 2, b = 41, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 21, g = 21, b = 21, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { r = 19, g = 42, b = 142, },
                    SkyLightColor = { b = 170, g = 63, r = 34, },
                    BottomColor = { b = 219, g = 181, r = 59, },
                    AmbientColor = { b = 68, g = 34, r = 30, },
                    TopColor = { r = 0, g = 19, b = 89, },
                },
                Moon = {
                    Color = { b = 0, g = 0, r = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = false,
                    RangeEnd = 32,
                    Color = { r = 0, g = 1, b = 37, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 22, g = 22, b = 22, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=50, b=100, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [6] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_blood",
        Name = GetS(301265),
        TemplateIdx = 6,
        SkyTex = {
            idx = 0,
            path = "",
        },
        SunTex = {
            idx = 2,
            path = "ugcenv/sun2.png",
        },
        MoonTex = {
            idx = 2,
            path = "ugcenv/moon2.png",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 124, g = 167, r = 178, },
                    Width = 2.2068965435028,
                },
                Sky = {
                    MidColor = { b = 77, g = 77, r = 209, },
                    SkyLightColor = { b = 52, g = 52, r = 192, },
                    BottomColor = { b = 93, g = 93, r = 252, },
                    AmbientColor = { b = 26, g = 34, r = 76, },
                    TopColor = { b = 22, g = 22, r = 117, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 35,
                    Enable = true,
                    Color = { b = 93, g = 93, r = 252, },
                    RangeStart = 4,
                },
                Cloud = {
                    Color = { b = 142, g = 142, r = 142, },
                    Density = 2.1,
                    Height = 10,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 91, g = 140, r = 183, },
                    Width = 2.5517240166664,
                },
                Sky = {
                    MidColor = { b = 107, g = 158, r = 255, },
                    SkyLightColor = { b = 43, g = 43, r = 216, },
                    BottomColor = { b = 74, g = 146, r = 247, },
                    AmbientColor = { b = 102, g = 102, r = 168, },
                    TopColor = { b = 71, g = 104, r = 237, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 33,
                    Enable = true,
                    Color = { b = 74, g = 146, r = 247, },
                    RangeStart = 3,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 61, g = 216, r = 255, },
                    Width = 2.4942526221275,
                },
                Sky = {
                    MidColor = { b = 44, g = 56, r = 224, },
                    SkyLightColor = { b = 17, g = 17, r = 247, },
                    BottomColor = { b = 48, g = 91, r = 209, },
                    AmbientColor = { b = 114, g = 114, r = 160, },
                    TopColor = { b = 132, g = 181, r = 255, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1.0344827920198,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 59,
                    Enable = true,
                    Color = { b = 48, g = 91, r = 209, },
                    RangeStart = 4,
                },
                Cloud = {
                    Color = { b = 43, g = 43, r = 43, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { b = 67, g = 156, r = 219, },
                    Width = 1.9770115613937,
                },
                Sky = {
                    MidColor = { b = 124, g = 174, r = 255, },
                    SkyLightColor = { b = 29, g = 92, r = 186, },
                    BottomColor = { b = 60, g = 88, r = 232, },
                    AmbientColor = { b = 63, g = 82, r = 181, },
                    TopColor = { b = 32, g = 32, r = 216, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 0.97701147198677,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 43,
                    Enable = true,
                    Color = { b = 60, g = 88, r = 232, },
                    RangeStart = 18,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 84, g = 146, r = 255, },
                    Width = 1.8620690703392,
                },
                Sky = {
                    MidColor = { b = 102, g = 102, r = 255, },
                    SkyLightColor = { b = 32, g = 100, r = 234, },
                    BottomColor = { b = 28, g = 110, r = 204, },
                    AmbientColor = { b = 16, g = 16, r = 89, },
                    TopColor = { b = 0, g = 0, r = 140, },
                },
                Moon = {
                    Color = { b = 107, g = 107, r = 244, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 61,
                    Enable = true,
                    Color = { b = 28, g = 110, r = 204, },
                    RangeStart = 1,
                },
                Cloud = {
                    Color = { b = 61, g = 61, r = 61, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { b = 66, g = 44, r = 35, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 20, g = 41, r = 183, },
                    SkyLightColor = { b = 27, g = 27, r = 71, },
                    BottomColor = { b = 49, g = 88, r = 196, },
                    AmbientColor = { b = 42, g = 42, r = 117, },
                    TopColor = { b = 3, g = 3, r = 35, },
                },
                Moon = {
                    Color = { b = 0, g = 115, r = 160, },
                    Width = 1.1494252830744,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 52,
                    Enable = true,
                    Color = { b = 49, g = 88, r = 196, },
                    RangeStart = 24,
                },
                Cloud = {
                    Color = { r = 26, g = 26, b = 26, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 5, g = 5, r = 102, },
                    SkyLightColor = { b = 28, g = 28, r = 140, },
                    BottomColor = { b = 24, g = 25, r = 100, },
                    AmbientColor = { b = 20, g = 50, r = 82, },
                    TopColor = { b = 65, g = 82, r = 168, },
                },
                Moon = {
                    Color = { b = 99, g = 99, r = 226, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = true,
                    Color = { b = 24, g = 25, r = 100, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 21, g = 21, r = 21, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { b = 121, g = 121, r = 124, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 99, g = 99, r = 99, },
                    SkyLightColor = { b = 56, g = 56, r = 156, },
                    BottomColor = { b = 114, g = 114, r = 114, },
                    AmbientColor = { b = 28, g = 28, r = 28, },
                    TopColor = { b = 40, g = 40, r = 40, },
                },
                Moon = {
                    Color = { b = 173, g = 173, r = 173, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = true,
                    Color = { b = 114, g = 114, r = 114, },
                    RangeStart = 6,
                },
                Cloud = {
                    Color = { r = 22, g = 22, b = 22, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=157, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [7] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_wasteland",
        Name = GetS(301267),
        TemplateIdx = 7,
        SkyTex = {
        },
        SunTex = {
            idx = 3,
            path = "ugcenv/sun3.png",
        },
        MoonTex = {
            idx = 3,
            path = "ugcenv/moon3.png",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 120, g = 132, r = 186, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 50, g = 70, r = 91, },
                    SkyLightColor = { b = 51, g = 69, r = 81, },
                    BottomColor = { b = 102, g = 119, r = 135, },
                    AmbientColor = { b = 38, g = 45, r = 58, },
                    TopColor = { b = 13, g = 27, r = 40, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 255, g = 109, b = 0, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { b = 58, g = 84, r = 112, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 44, g = 133, r = 211, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 126, g = 163, r = 204, },
                    SkyLightColor = { b = 77, g = 103, r = 124, },
                    BottomColor = { b = 87, g = 128, r = 178, },
                    AmbientColor = { b = 50, g = 73, r = 96, },
                    TopColor = { b = 148, g = 200, r = 232, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 177, g = 227, b = 255, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { b = 58, g = 121, r = 158, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 112, g = 192, r = 249, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 63, g = 127, r = 191, },
                    SkyLightColor = { b = 110, g = 174, r = 216, },
                    BottomColor = { b = 72, g = 120, r = 175, },
                    AmbientColor = { b = 77, g = 123, r = 160, },
                    TopColor = { b = 140, g = 206, r = 247, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { b = 57, g = 128, r = 147, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { b = 51, g = 124, r = 234, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 124, g = 184, r = 239, },
                    SkyLightColor = { b = 39, g = 120, r = 196, },
                    BottomColor = { b = 93, g = 145, r = 193, },
                    AmbientColor = { b = 53, g = 76, r = 114, },
                    TopColor = { b = 41, g = 102, r = 178, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { b = 31, g = 90, r = 124, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 112, g = 126, r = 201, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 67, g = 130, r = 193, },
                    SkyLightColor = { b = 28, g = 72, r = 168, },
                    BottomColor = { b = 72, g = 155, r = 206, },
                    AmbientColor = { b = 10, g = 66, r = 68, },
                    TopColor = { b = 25, g = 72, r = 119, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 254, g = 128, b = 50, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { b = 10, g = 56, r = 119, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { r = 53, g = 70, b = 112, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 45, g = 77, r = 122, },
                    SkyLightColor = { b = 33, g = 63, r = 114, },
                    BottomColor = { b = 64, g = 79, r = 114, },
                    AmbientColor = { b = 12, g = 52, r = 48, },
                    TopColor = { b = 5, g = 23, r = 58, },
                },
                Moon = {
                    Color = { b = 21, g = 119, r = 211, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 0, g = 1, b = 36, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { r = 26, g = 26, b = 26, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 43, g = 56, r = 76, },
                    SkyLightColor = { b = 26, g = 47, r = 86, },
                    BottomColor = { b = 32, g = 43, r = 63, },
                    AmbientColor = { b = 10, g = 44, r = 40, },
                    TopColor = { b = 56, g = 81, r = 124, },
                },
                Moon = {
                    Color = { b = 21, g = 119, r = 211, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 0, g = 2, b = 41, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { b = 20, g = 24, r = 43, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { b = 47, g = 82, r = 153, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 56, g = 63, r = 84, },
                    SkyLightColor = { b = 50, g = 66, r = 99, },
                    BottomColor = { b = 50, g = 65, r = 89, },
                    AmbientColor = { b = 32, g = 43, r = 63, },
                    TopColor = { b = 24, g = 29, r = 51, },
                },
                Moon = {
                    Color = { b = 78, g = 103, r = 114, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = false,
                    Color = { r = 0, g = 1, b = 37, },
                    RangeEnd = 32,
                    RangeStart = 10,
                },
                Cloud = {
                    Density = 2.1,
                    Speed = 1,
                    Height = 20,
                    Color = { r = 22, g = 22, b = 22, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=255, g=187, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [8] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_tech",
        Name = GetS(301268),
        TemplateIdx = 8,
        SkyTex = {
            idx = 0,
            path = "",
        },
        SunTex = {
            idx = 4,
            path = "ugcenv/sun4.png",
        },
        MoonTex = {
            idx = 5,
            path = "ugcenv/moon5.png",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 140, g = 140, r = 140, },
                    Width = 2.2068965435028,
                },
                Sky = {
                    MidColor = { b = 173, g = 172, r = 171, },
                    SkyLightColor = { b = 107, g = 107, r = 107, },
                    BottomColor = { b = 137, g = 137, r = 137, },
                    AmbientColor = { r = 11, g = 29, b = 52, },
                    TopColor = { b = 214, g = 214, r = 214, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 255, g = 109, b = 0, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 142, g = 142, r = 142, },
                    Density = 2.1,
                    Height = 10,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 86, g = 86, r = 86, },
                    Width = 2.5517240166664,
                },
                Sky = {
                    MidColor = { b = 104, g = 104, r = 104, },
                    SkyLightColor = { b = 168, g = 168, r = 168, },
                    BottomColor = { b = 109, g = 109, r = 109, },
                    AmbientColor = { b = 68, g = 48, r = 52, },
                    TopColor = { b = 214, g = 214, r = 214, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 177, g = 227, b = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 76, g = 76, r = 76, },
                    Width = 2.4942526221275,
                },
                Sky = {
                    MidColor = { b = 173, g = 172, r = 171, },
                    SkyLightColor = { b = 242, g = 228, r = 205, },
                    BottomColor = { b = 137, g = 137, r = 137, },
                    AmbientColor = { b = 112, g = 96, r = 86, },
                    TopColor = { b = 214, g = 214, r = 214, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1.0344827920198,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 100,
                    Enable = false,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeStart = 100,
                },
                Cloud = {
                    Color = { b = 43, g = 43, r = 43, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { b = 58, g = 58, r = 58, },
                    Width = 1.9770115613937,
                },
                Sky = {
                    MidColor = { b = 226, g = 226, r = 226, },
                    SkyLightColor = { b = 94, g = 94, r = 94, },
                    BottomColor = { b = 111, g = 111, r = 112, },
                    AmbientColor = { b = 84, g = 84, r = 84, },
                    TopColor = { b = 173, g = 173, r = 173, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 0.97701147198677,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 177, g = 226, b = 255, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 140, g = 140, b = 140, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 114, g = 114, r = 114, },
                    Width = 1.8620690703392,
                },
                Sky = {
                    MidColor = { b = 137, g = 137, r = 137, },
                    SkyLightColor = { b = 86, g = 86, r = 86, },
                    BottomColor = { b = 191, g = 191, r = 191, },
                    AmbientColor = { b = 40, g = 40, r = 40, },
                    TopColor = { b = 107, g = 107, r = 107, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 254, g = 128, b = 50, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 61, g = 61, r = 61, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { b = 66, g = 44, r = 35, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 68, g = 68, r = 68, },
                    SkyLightColor = { b = 58, g = 58, r = 58, },
                    BottomColor = { b = 147, g = 147, r = 147, },
                    AmbientColor = { b = 42, g = 42, r = 48, },
                    TopColor = { b = 33, g = 33, r = 38, },
                },
                Moon = {
                    Color = { b = 221, g = 221, r = 221, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 0, g = 1, b = 36, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 26, g = 26, b = 26, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 33, g = 33, r = 33, },
                    SkyLightColor = { b = 89, g = 89, r = 89, },
                    BottomColor = { b = 48, g = 48, r = 48, },
                    AmbientColor = { b = 51, g = 51, r = 51, },
                    TopColor = { b = 145, g = 145, r = 145, },
                },
                Moon = {
                    Color = { b = 206, g = 206, r = 206, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 0, g = 2, b = 41, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { b = 21, g = 21, r = 21, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { b = 121, g = 121, r = 124, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 99, g = 99, r = 99, },
                    SkyLightColor = { b = 56, g = 56, r = 56, },
                    BottomColor = { b = 114, g = 114, r = 114, },
                    AmbientColor = { b = 28, g = 28, r = 28, },
                    TopColor = { b = 40, g = 40, r = 40, },
                },
                Moon = {
                    Color = { b = 173, g = 173, r = 173, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    RangeEnd = 32,
                    Enable = false,
                    Color = { r = 0, g = 1, b = 37, },
                    RangeStart = 10,
                },
                Cloud = {
                    Color = { r = 22, g = 22, b = 22, },
                    Density = 2.1,
                    Height = 20,
                    Speed = 1,
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=100, g=115, b=158, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [9] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_poison",
        Name = GetS(301266),
        TemplateIdx = 9,
        SkyTex = {
            idx = 0,
            path = "",
        },
        SunTex = {
            idx = 3,
            path = "ugcenv/sun3.png",
        },
        MoonTex = {
            idx = 3,
            path = "ugcenv/moon3.png",
        },
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sun = {
                    Color = { b = 124, g = 226, r = 223, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 109, g = 191, r = 144, },
                    SkyLightColor = { b = 29, g = 91, r = 18, },
                    BottomColor = { b = 89, g = 178, r = 99, },
                    AmbientColor = { b = 104, g = 101, r = 38, },
                    TopColor = { b = 87, g = 145, r = 52, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 32,
                    Color = { b = 89, g = 178, r = 99, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { b = 63, g = 170, r = 169, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [2] = {
                Sun = {
                    Color = { b = 193, g = 250, r = 255, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 156, g = 170, r = 109, },
                    SkyLightColor = { b = 191, g = 237, r = 142, },
                    BottomColor = { b = 74, g = 175, r = 43, },
                    AmbientColor = { b = 104, g = 67, r = 50, },
                    TopColor = { b = 166, g = 237, r = 220, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 34,
                    Color = { b = 74, g = 175, r = 43, },
                    RangeStart = 18,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 140, g = 140, b = 140, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sun = {
                    Color = { b = 181, g = 255, r = 240, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 99, g = 163, r = 57, },
                    SkyLightColor = { b = 255, g = 255, r = 255, },
                    BottomColor = { b = 34, g = 170, r = 72, },
                    AmbientColor = { b = 31, g = 130, r = 46, },
                    TopColor = { b = 118, g = 242, r = 213, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 73,
                    Color = { b = 34, g = 170, r = 72, },
                    RangeStart = 3,
                },
                Cloud = {
                    Speed = 1,
                    Density = 4,
                    Height = 10,
                    Color = { r = 134, g = 134, b = 134, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [4] = {
                Sun = {
                    Color = { r = 249, g = 199, b = 147, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 37, g = 140, r = 28, },
                    SkyLightColor = { b = 0, g = 173, r = 144, },
                    BottomColor = { b = 2, g = 132, r = 58, },
                    AmbientColor = { b = 11, g = 68, r = 53, },
                    TopColor = { b = 123, g = 229, r = 227, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 40,
                    Color = { b = 2, g = 132, r = 58, },
                    RangeStart = 12,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { b = 92, g = 92, r = 94, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [5] = {
                Sun = {
                    Color = { b = 10, g = 116, r = 135, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 0, g = 125, r = 150, },
                    SkyLightColor = { b = 1, g = 147, r = 72, },
                    BottomColor = { b = 0, g = 135, r = 103, },
                    AmbientColor = { b = 0, g = 51, r = 39, },
                    TopColor = { b = 59, g = 124, r = 6, },
                },
                Moon = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 50,
                    Color = { b = 0, g = 135, r = 103, },
                    RangeStart = 3,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { b = 86, g = 86, r = 86, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [6] = {
                Sun = {
                    Color = { b = 112, g = 70, r = 53, },
                    Width = 1.0000000149012,
                },
                Sky = {
                    MidColor = { b = 19, g = 140, r = 130, },
                    SkyLightColor = { b = 18, g = 81, r = 10, },
                    BottomColor = { b = 14, g = 94, r = 71, },
                    AmbientColor = { b = 37, g = 51, r = 6, },
                    TopColor = { b = 59, g = 81, r = 0, },
                },
                Moon = {
                    Color = { b = 103, g = 229, r = 193, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 50,
                    Color = { b = 14, g = 94, r = 71, },
                    RangeStart = 24,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 26, g = 26, b = 26, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [7] = {
                Sun = {
                    Color = { r = 0, g = 0, b = 0, },
                    Width = 1,
                },
                Sky = {
                    MidColor = { b = 86, g = 78, r = 4, },
                    SkyLightColor = { b = 46, g = 65, r = 31, },
                    BottomColor = { b = 51, g = 53, r = 1, },
                    AmbientColor = { b = 85, g = 94, r = 0, },
                    TopColor = { b = 39, g = 99, r = 0, },
                },
                Moon = {
                    Color = { b = 141, g = 211, r = 205, },
                    Width = 0.48275861889124,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 23,
                    Color = { b = 51, g = 53, r = 1, },
                    RangeStart = 3,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 21, g = 21, b = 21, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
            [8] = {
                Sun = {
                    Color = { b = 59, g = 239, r = 65, },
                    Width = 1.1149425804615,
                },
                Sky = {
                    MidColor = { b = 153, g = 158, r = 26, },
                    SkyLightColor = { b = 39, g = 61, r = 0, },
                    BottomColor = { b = 77, g = 89, r = 8, },
                    AmbientColor = { b = 61, g = 52, r = 36, },
                    TopColor = { b = 64, g = 132, r = 0, },
                },
                Moon = {
                    Color = { b = 64, g = 160, r = 141, },
                    Width = 1,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = true,
                    RangeEnd = 32,
                    Color = { b = 77, g = 89, r = 8, },
                    RangeStart = 10,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = { r = 22, g = 22, b = 22, },
                },
                                
                Water = {
                    Enable = true,
                    Color = {r=0, g=255, b=0, a=200},
                    ReflectStrength = 100,
                },
        
                Wind = {
                    Strength = 20
                },
            },
        },
    },
    [10] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_ink",
        Name = GetS(301337),
        TemplateIdx = 10,
        SkyTex = {
            idx = 3,
            path = "ugcenv/shuimo.png",
            isCubeTex = true,
        },
        SunTex = {},
        MoonTex = {},
        IsScroll = false,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 248, g = 124, b = 6},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
                
            },
            [2] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 140, g = 140, b = 140},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 134, g = 134, b = 134},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [4] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 140, g = 140, b = 140},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [5] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 248, g = 124, b = 6},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [6] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 26, g = 26, b = 26},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [7] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 21, g = 21, b = 21},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [8] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=153, g=153, b=153},
                    AmbientColor = {r=92, g=99, b=104},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=181, g=181, b=181},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
        },
    },
    [11] = {
        iconPath = "ui://ugc_resourcesidebar/icon_skyTemp_inkGreen",
        Name = GetS(301339),
        TemplateIdx = 11,
        SkyTex = {
            idx = 4,
            path = "ugcenv/shuimo2.png",
            isCubeTex = true,
        },
        SunTex = {},
        MoonTex = {},
        IsScroll = false,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },  
            },
            [2] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },  
            },
            [3] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },  
            },
            [4] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
            [5] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },  
            },
            [6] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },  
            },
            [7] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                },  
            },
            [8] = {
                Sky = {
                    TopColor = {r=255, g=255, b=255},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=255, g=255, b=255},
                    SkyLightColor = {r=209, g=201, b=181},
                    AmbientColor = {r=56, g=55, b=39},
                },

                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },

                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0,
                },
                
                Star = {
                    Density=0.0,
                },

                Fog = {
                    Enable = true,
                    Color = {r=209, g=181, b=117},
                    RangeStart = 3,
                    RangeEnd = 200,
                },

                Cloud = { 
                    Speed = 1, 
                    Density = 2.1, 
                    Height = 20, 
                    Color = {r = 22, g = 22, b = 22},
                },
                                
                Water = {
                    Enable = false,
                    Color = {r=0, g=100, b=204, a=255},
                    ReflectStrength = 100,
                },

                Wind = {
                    Strength = 20
                }, 
            },
        },
    },
    --森林
    [12] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_forest",
        Name = GetS(301341),
        TemplateIdx = 12,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {--6点
                Sky = {
                    TopColor = {r=88, g=130, b=160},
                    MidColor = {r=145, g=167, b=214},
                    BottomColor = {r=137, g=158, b=188},
                    SkyLightColor = {r=112, g=103, b=97},
                    AmbientColor = {r=15, g=32, b=56},
                },
                Sun = {
                    Color = {r=229, g=109, b=61},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.01,
                },
                Fog = {
                    Enable = true,
                    Color = {r=100, g=142, b=201},
                    RangeStart = -11,
                    RangeEnd = 140,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2,
                    Height = 20,
                    Color = {r=247, g=124, b=5},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [2] = {--8点
                Sky = {
                    TopColor = {r=215, g=223, b=232},
                    MidColor = {r=150, g=208, b=242},
                    BottomColor = {r=126, g=196, b=245},
                    SkyLightColor = {r=234, g=201, b=157},
                    AmbientColor = {r=22.95, g=17.85, b=68.85},
                },
                Sun = {
                    Color = {r=244.8, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=122, g=188, b=232},
                    RangeStart = -1,
                    RangeEnd = 170,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=113, g=109, b=125},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [3] = {--12点
                Sky = {
                    TopColor = {r=146, g=178, b=204},
                    MidColor = {r=170, g=205, b=226},
                    BottomColor = {r=187, g=232, b=229},
                    SkyLightColor = {r=242, g=230, b=181},
                    AmbientColor = {r=22.95, g=17.85, b=68.85},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=165, g=210, b=229},
                    RangeStart = 80,
                    RangeEnd = 200,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=240, g=165, b=150},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [4] = {--16点
                Sky = {
                    TopColor = {r=141, g=181, b=211},
                    MidColor = {r=178, g=213, b=232},
                    BottomColor = {r=158, g=204, b=211},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=183, g=166, b=132},
                    RangeStart = 70,
                    RangeEnd = 180,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=197, g=202, b=249},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [5] = {--18点
                Sky = {
                    TopColor = {r=158, g=195, b=247},
                    MidColor = {r=186, g=220, b=252},
                    BottomColor = {r=255, g=205, b=165},
                    SkyLightColor = {r=232.05, g=207, b=197},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=209, g=162, b=108},
                    RangeStart = 60,
                    RangeEnd = 170,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=117, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [6] = {--20点
                Sky = {
                    TopColor = {r=40, g=45, b=51},
                    MidColor = {r=63, g=80, b=96},
                    BottomColor = {r=116, g=145, b=147},
                    SkyLightColor = {r=29, g=38, b=66},
                    AmbientColor = {r=15, g=32, b=48},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    Color = {r=48, g=76, b=89},
                    RangeStart = 0,
                    RangeEnd = 110,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=0, g=0, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [7] = {--24点
                Sky = {
                    TopColor = {r=40, g=48, b=63},
                    MidColor = {r=21, g=53, b=79},
                    BottomColor = {r=50, g=71, b=104},
                    SkyLightColor = {r=29, g=30, b=40},
                    AmbientColor = {r=48, g=48, b=48},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=35, g=53, b=81},
                    RangeStart = 0,
                    RangeEnd = 110,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=21, g=21, b=21},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [8] = {--4点
                Sky = {
                    TopColor = {r=32, g=42, b=53},
                    MidColor = {r=87, g=118, b=127},
                    BottomColor = {r=51, g=90, b=132},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.4,
                },
                Fog = {
                    Enable = true,
                    Color = {r=40, g=63, b=89},
                    RangeStart = 0,
                    RangeEnd = 110,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=0, g=0, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=204, b=178, a=78},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
        },
    },
    -- 沙漠
    [13] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_desert",
        Name = GetS(301342),
        TemplateIdx = 13,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,
        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=220, g=141, b=98},
                    MidColor = {r=248, g=170, b=122},
                    BottomColor = {r=255, g=213, b=142},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=255, g=213, b=142},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=248, g=113, b=5},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=133, g=182, b=185},
                    MidColor = {r=183, g=189, b=161},
                    BottomColor = {r=245, g=196, b=129},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=245, g=196, b=129},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=178, g=105, b=57},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=84, g=214, b=237},
                    MidColor = {r=83, g=192, b=235},
                    BottomColor = {r=229, g=173, b=85},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=229, g=173, b=85},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=149, g=129, b=63},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=91, g=179, b=223},
                    MidColor = {r=246, g=195, b=114},
                    BottomColor = {r=245, g=171, b=100},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=245, g=171, b=100},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=226, g=87, b=13},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=123, g=125, b=103},
                    MidColor = {r=238, g=111, b=45},
                    BottomColor = {r=254, g=170, b=64},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=254, g=170, b=64},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=245, g=111, b=19},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=25, g=26, b=23},
                    MidColor = {r=150, g=82, b=43},
                    BottomColor = {r=195, g=114, b=64},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=195, g=114, b=64},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=105, g=37, b=37},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=9, g=15, b=25},
                    MidColor = {r=52, g=69, b=90},
                    BottomColor = {r=57, g=76, b=95},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.999,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=57, g=76, b=95},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=148, g=166, b=211},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=50, g=46, b=28},
                    MidColor = {r=127, g=81, b=57},
                    BottomColor = {r=161, g=98, b=64},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    RangeEnd = 180,
                    Enable = true,
                    Color = {r=161, g=98, b=64},
                    RangeStart = -30,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=22, g=22, b=22},
                },
                Water = {
                    Enable = true,
                    Color = {r=76, g=204, b=331, a=234},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
        },
    },
    -- 冰原
    [14] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_icefield",
        Name = GetS(301343),
        TemplateIdx = 14,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,
        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=74, g=117, b=165},
                    MidColor = {r=234, g=232, b=233},
                    BottomColor = {r=192, g=151, b=156},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.05,
                },
                Fog = {
                    Enable = true,
                    Color = {r=192, g=151, b=156},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=98, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=133, g=160, b=192},
                    MidColor = {r=154, g=179, b=205},
                    BottomColor = {r=216, g=215, b=214},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=216, g=215, b=214},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=221, g=191, b=255},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=141, g=164, b=206},
                    MidColor = {r=214, g=217, b=230},
                    BottomColor = {r=175, g=199, b=240},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=175, g=199, b=240},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=160, g=147, b=163},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=95, g=136, b=182},
                    MidColor = {r=168, g=193, b=216},
                    BottomColor = {r=207, g=180, b=183},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=207, g=180, b=183},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=161, g=136, b=123},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=80, g=143, b=197},
                    MidColor = {r=161, g=127, b=137},
                    BottomColor = {r=190, g=142, b=162},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=190, g=142, b=162},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=178, g=74, b=14},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=30, g=36, b=103},
                    MidColor = {r=146, g=125, b=180},
                    BottomColor = {r=62, g=60, b=146},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=62, g=60, b=146},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=153, g=81, b=146},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=2, g=23, b=50},
                    MidColor = {r=39, g=113, b=87},
                    BottomColor = {r=0, g=43, b=105},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    Color = {r=0, g=43, b=105},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=57, g=82, b=173},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=37, g=43, b=76},
                    MidColor = {r=123, g=129, b=182},
                    BottomColor = {r=96, g=95, b=142},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=96, g=95, b=142},
                    RangeStart = -60,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=104, g=125, b=168},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 沼泽
    [15] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_swamp",
        Name = GetS(301344),
        TemplateIdx = 15,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {--6点
                Sky = {
                    TopColor = {r=155, g=210, b=171},
                    MidColor = {r=196, g=219, b=165},
                    BottomColor = {r=84, g=144, b=111},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.01,
                },
                Fog = {
                    Enable = true,
                    Color = {r=84, g=144, b=111},
                    RangeStart = -30,
                    RangeEnd = 100,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=233, g=168, b=42},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [2] = {--8点
                Sky = {
                    TopColor = {r=178, g=197, b=194},
                    MidColor = {r=184, g=209, b=206},
                    BottomColor = {r=152, g=184, b=180},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=152, g=184, b=180},
                    RangeStart = -24,
                    RangeEnd = 140,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=168, g=127, b=78},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [3] = {--12点
                Sky = {
                    TopColor = {r=225, g=224, b=225},
                    MidColor = {r=156, g=187, b=203},
                    BottomColor = {r=101, g=133, b=166},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=101, g=133, b=166},
                    RangeStart = 0,
                    RangeEnd = 180,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=202, g=132, b=105},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [4] = {--16点
                Sky = {
                    TopColor = {r=166, g=189, b=197},
                    MidColor = {r=171, g=190, b=196},
                    BottomColor = {r=86, g=151, b=151},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=86, g=151, b=151},
                    RangeStart = -15,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=223, g=57, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [5] = {--18点
                Sky = {
                    TopColor = {r=88, g=91, b=96},
                    MidColor = {r=244, g=231, b=186},
                    BottomColor = {r=168, g=145, b=147},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=168, g=145, b=147},
                    RangeStart = -20,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=214, g=123, b=47},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [6] = {--20点
                Sky = {
                    TopColor = {r=39, g=54, b=74},
                    MidColor = {r=51, g=81, b=28},
                    BottomColor = {r=108, g=147, b=79},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=108, g=147, b=79},
                    RangeStart = -20,
                    RangeEnd = 100,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=26, g=26, b=26},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [7] = {--24点
                Sky = {
                    TopColor = {r=35, g=48, b=64},
                    MidColor = {r=68, g=96, b=50},
                    BottomColor = {r=94, g=125, b=74},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=94, g=125, b=74},
                    RangeStart = -30,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=26, g=26, b=26},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [8] = {--4点
                Sky = {
                    TopColor = {r=91, g=96, b=64},
                    MidColor = {r=148, g=166, b=144},
                    BottomColor = {r=56, g=85, b=94},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=56, g=85, b=94},
                    RangeStart = -30,
                    RangeEnd = 90,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=87, g=163, b=82},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=165, b=51, a=229},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
        },
    },
    -- 火山
    [16] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_volcano",
        Name = GetS(301345),
        TemplateIdx = 16,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=7, g=7, b=7},
                    MidColor = {r=81, g=77, b=102},
                    BottomColor = {r=74, g=66, b=107},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=74, g=66, b=107},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=110, g=49, b=21},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=199, g=195, b=192},
                    MidColor = {r=220, g=198, b=201},
                    BottomColor = {r=75, g=103, b=151},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=75, g=103, b=151},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=218, g=120, b=146},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=214, g=206, b=230},
                    MidColor = {r=201, g=198, b=227},
                    BottomColor = {r=97, g=96, b=130},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=97, g=96, b=130},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=114, g=124, b=141},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=46, g=35, b=50},
                    MidColor = {r=99, g=108, b=147},
                    BottomColor = {r=111, g=99, b=137},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=111, g=99, b=137},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=122, g=63, b=31},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=30, g=39, b=68},
                    MidColor = {r=39, g=65, b=126},
                    BottomColor = {r=123, g=89, b=103},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=123, g=89, b=103},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=209, g=115, b=58},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=42, g=21, b=37},
                    MidColor = {r=69, g=45, b=71},
                    BottomColor = {r=106, g=47, b=67},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.3,
                },
                Fog = {
                    Enable = true,
                    Color = {r=106, g=47, b=67},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=52, g=32, b=45},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=61, g=49, b=67},
                    MidColor = {r=105, g=96, b=107},
                    BottomColor = {r=91, g=63, b=50},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.3,
                },
                Fog = {
                    Enable = true,
                    Color = {r=91, g=63, b=50},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=60, g=28, b=18},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=26, g=15, b=49},
                    MidColor = {r=197, g=77, b=185},
                    BottomColor = {r=57, g=13, b=75},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.4,
                },
                Fog = {
                    Enable = true,
                    Color = {r=57, g=13, b=75},
                    RangeStart = -50,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=127, g=61, b=121},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=102, b=204, a=247},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 20,
                },
            },
        },
    },
    -- 雨林
    [17] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_rainforest",
        Name = GetS(301346),
        TemplateIdx = 17,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=233, g=231, b=238},
                    MidColor = {r=194, g=179, b=208},
                    BottomColor = {r=89, g=104, b=146},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=89, g=104, b=146},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=247, g=124, b=5},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=159, g=175, b=200},
                    MidColor = {r=180, g=224, b=240},
                    BottomColor = {r=118, g=161, b=175},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=118, g=161, b=175},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=194, g=153, b=131},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=215, g=224, b=227},
                    MidColor = {r=202, g=219, b=225},
                    BottomColor = {r=97, g=150, b=173},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=97, g=150, b=173},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=174, g=152, b=187},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=178, g=197, b=194},
                    MidColor = {r=176, g=202, b=199},
                    BottomColor = {r=120, g=151, b=148},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=120, g=151, b=148},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=170, g=139, b=121},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=216, g=196, b=221},
                    MidColor = {r=214, g=194, b=220},
                    BottomColor = {r=67, g=84, b=125},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=67, g=84, b=125},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=232, g=111, b=64},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=41, g=85, b=156},
                    MidColor = {r=82, g=117, b=165},
                    BottomColor = {r=34, g=58, b=23},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=34, g=58, b=23},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=145, g=173, b=202},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=61, g=89, b=46},
                    MidColor = {r=87, g=125, b=69},
                    BottomColor = {r=89, g=127, b=71},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=89, g=127, b=71},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=97, g=137, b=80},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=157, g=163, b=150},
                    MidColor = {r=133, g=145, b=119},
                    BottomColor = {r=87, g=109, b=64},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=87, g=109, b=64},
                    RangeStart = -20,
                    RangeEnd = 80,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=107, g=99, b=115},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=51, a=242},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 丛林
    [18] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_jungle",
        Name = GetS(301347),
        TemplateIdx = 18,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=160, g=158, b=241},
                    MidColor = {r=235, g=190, b=197},
                    BottomColor = {r=91, g=167, b=205},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=91, g=167, b=205},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=247, g=124, b=5},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=172, g=160, b=210},
                    MidColor = {r=237, g=190, b=196},
                    BottomColor = {r=184, g=243, b=252},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=184, g=243, b=252},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=252, g=140, b=62},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=56, g=132, b=210},
                    MidColor = {r=148, g=186, b=233},
                    BottomColor = {r=187, g=206, b=236},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=187, g=206, b=236},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=129, g=162, b=192},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=144, g=129, b=209},
                    MidColor = {r=144, g=142, b=199},
                    BottomColor = {r=157, g=187, b=205},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=157, g=187, b=205},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=202, g=121, b=75},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=96, g=73, b=151},
                    MidColor = {r=164, g=78, b=103},
                    BottomColor = {r=255, g=142, b=69},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.05,
                },
                Fog = {
                    Enable = true,
                    Color = {r=255, g=142, b=69},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=232, g=111, b=64},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=2, g=32, b=81},
                    MidColor = {r=22, g=86, b=134},
                    BottomColor = {r=37, g=156, b=214},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=37, g=156, b=214},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=57, g=146, b=240},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=0, g=23, b=54},
                    MidColor = {r=23, g=59, b=119},
                    BottomColor = {r=23, g=100, b=105},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=23, g=100, b=105},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=151, g=57, b=153},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=50, g=55, b=85},
                    MidColor = {r=49, g=54, b=96},
                    BottomColor = {r=41, g=131, b=139},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=41, g=131, b=139},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=29, g=17, b=33},
                },
                Water = {
                    Enable = true,
                    Color = {r=0, g=306, b=127, a=209},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 针叶林
    [19] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_taiga",
        Name = GetS(301348),
        TemplateIdx = 19,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=161, g=174, b=206},
                    MidColor = {r=255, g=208, b=177},
                    BottomColor = {r=119, g=109, b=146},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=119, g=109, b=146},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=122, g=66, b=61},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=185, g=240, b=255},
                    MidColor = {r=86, g=151, b=177},
                    BottomColor = {r=58, g=116, b=141},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=58, g=116, b=141},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=62, g=103, b=110},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=34, g=83, b=161},
                    MidColor = {r=161, g=197, b=235},
                    BottomColor = {r=93, g=146, b=214},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=93, g=146, b=214},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=129, g=115, b=115},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=65, g=117, b=194},
                    MidColor = {r=255, g=255, b=255},
                    BottomColor = {r=65, g=151, b=185},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=65, g=151, b=185},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=113, g=76, b=61},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=99, g=149, b=202},
                    MidColor = {r=250, g=171, b=71},
                    BottomColor = {r=129, g=114, b=160},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=129, g=114, b=160},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=111, g=51, b=63},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=11, g=13, b=15},
                    MidColor = {r=98, g=123, b=138},
                    BottomColor = {r=1, g=52, b=64},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=1, g=52, b=64},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=93, g=45, b=81},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=12, g=14, b=14},
                    MidColor = {r=18, g=45, b=57},
                    BottomColor = {r=12, g=86, b=84},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    Color = {r=12, g=86, b=84},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=175, g=172, b=172},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=23, g=57, b=57},
                    MidColor = {r=174, g=234, b=209},
                    BottomColor = {r=93, g=161, b=153},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.3,
                },
                Fog = {
                    Enable = true,
                    Color = {r=93, g=161, b=153},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=180, g=180, b=180},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=102, b=25, a=216},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 高山
    [20] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_extremehills",
        Name = GetS(301349),
        TemplateIdx = 20,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=153, g=79, b=91},
                    MidColor = {r=254, g=212, b=120},
                    BottomColor = {r=254, g=184, b=156},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=254, g=184, b=156},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=236, g=43, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=70, g=139, b=194},
                    MidColor = {r=219, g=220, b=225},
                    BottomColor = {r=61, g=126, b=182},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=61, g=126, b=182},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=91, g=98, b=105},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=128, g=193, b=221},
                    MidColor = {r=176, g=223, b=239},
                    BottomColor = {r=81, g=155, b=204},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=81, g=155, b=204},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=129, g=115, b=115},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=89, g=167, b=206},
                    MidColor = {r=195, g=218, b=236},
                    BottomColor = {r=35, g=90, b=152},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=35, g=90, b=152},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=113, g=76, b=61},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=94, g=84, b=128},
                    MidColor = {r=249, g=126, b=67},
                    BottomColor = {r=254, g=179, b=66},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=254, g=179, b=66},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=195, g=103, b=62},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=105, g=115, b=91},
                    MidColor = {r=216, g=161, b=70},
                    BottomColor = {r=248, g=72, b=49},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=248, g=72, b=49},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=98, g=59, b=88},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=43, g=136, b=180},
                    MidColor = {r=12, g=57, b=96},
                    BottomColor = {r=6, g=17, b=37},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=6, g=17, b=37},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=42, g=137, b=181},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=149, g=197, b=219},
                    MidColor = {r=74, g=140, b=175},
                    BottomColor = {r=38, g=86, b=117},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.4,
                },
                Fog = {
                    Enable = true,
                    Color = {r=38, g=86, b=117},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=180, g=180, b=180},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=191, b=242, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 海洋
    [21] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_ocean",
        Name = GetS(301350),
        TemplateIdx = 21,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=53, g=107, b=193},
                    MidColor = {r=155, g=191, b=222},
                    BottomColor = {r=245, g=169, b=121},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=245, g=169, b=121},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=124, g=89, b=129},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=59, g=181, b=205},
                    MidColor = {r=92, g=204, b=205},
                    BottomColor = {r=140, g=250, b=192},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=140, g=250, b=192},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=153, g=128, b=68},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=26, g=152, b=254},
                    MidColor = {r=51, g=170, b=254},
                    BottomColor = {r=144, g=208, b=254},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=144, g=208, b=254},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=121, g=145, b=156},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=81, g=112, b=167},
                    MidColor = {r=177, g=129, b=171},
                    BottomColor = {r=172, g=192, b=222},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=172, g=192, b=222},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=197, g=90, b=32},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=14, g=53, b=106},
                    MidColor = {r=28, g=88, b=144},
                    BottomColor = {r=249, g=131, b=62},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=249, g=131, b=62},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=97, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=0, g=3, b=76},
                    MidColor = {r=245, g=208, b=146},
                    BottomColor = {r=221, g=103, b=93},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    Color = {r=221, g=103, b=93},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=64, g=18, b=48},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=15, g=17, b=76},
                    MidColor = {r=32, g=34, b=115},
                    BottomColor = {r=61, g=57, b=142},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.999,
                },
                Fog = {
                    Enable = true,
                    Color = {r=61, g=57, b=142},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=51, g=50, b=128},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=247, g=206, b=184},
                    MidColor = {r=115, g=157, b=201},
                    BottomColor = {r=85, g=122, b=167},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=85, g=122, b=167},
                    RangeStart = 0,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=141, g=108, b=102},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=433, b=561, a=221},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 空岛
    [22] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_airland",
        Name = GetS(301351),
        TemplateIdx = 22,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=78, g=137, b=235},
                    MidColor = {r=78, g=229, b=250},
                    BottomColor = {r=237, g=132, b=50},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.02,
                },
                Fog = {
                    Enable = true,
                    Color = {r=237, g=132, b=50},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=129, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=119, g=171, b=246},
                    MidColor = {r=142, g=199, b=170},
                    BottomColor = {r=220, g=224, b=198},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=220, g=224, b=198},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=123, b=65},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=31, g=87, b=169},
                    MidColor = {r=16, g=180, b=255},
                    BottomColor = {r=182, g=211, b=247},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=182, g=211, b=247},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=233, g=182, b=51},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=195, g=213, b=223},
                    MidColor = {r=135, g=168, b=189},
                    BottomColor = {r=106, g=140, b=165},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=106, g=140, b=165},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=226, g=81, b=15},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=255, g=227, b=190},
                    MidColor = {r=254, g=223, b=188},
                    BottomColor = {r=213, g=143, b=96},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=213, g=143, b=96},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=19, g=8, b=9},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=38, g=97, b=105},
                    MidColor = {r=25, g=73, b=103},
                    BottomColor = {r=21, g=51, b=79},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=21, g=51, b=79},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=145, g=173, b=202},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=12, g=18, b=25},
                    MidColor = {r=25, g=45, b=60},
                    BottomColor = {r=41, g=63, b=75},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.85,
                },
                Fog = {
                    Enable = true,
                    Color = {r=41, g=63, b=75},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=109, g=135, b=153},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=32, g=56, b=54},
                    MidColor = {r=45, g=84, b=77},
                    BottomColor = {r=92, g=124, b=101},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=92, g=124, b=101},
                    RangeStart = -10,
                    RangeEnd = 150,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=89, g=254, b=242},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=127, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 盆地
    [23] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_basin",
        Name = GetS(301352),
        TemplateIdx = 23,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=75, g=96, b=175},
                    MidColor = {r=247, g=118, b=52},
                    BottomColor = {r=118, g=166, b=238},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.01,
                },
                Fog = {
                    Enable = true,
                    Color = {r=118, g=166, b=238},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=73, b=82},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=46, g=93, b=188},
                    MidColor = {r=61, g=132, b=247},
                    BottomColor = {r=142, g=185, b=255},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=142, g=185, b=255},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=240, g=165, b=150},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=46, g=93, b=188},
                    MidColor = {r=61, g=132, b=247},
                    BottomColor = {r=128, g=214, b=255},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=128, g=214, b=255},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=240, g=165, b=150},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=24, g=80, b=154},
                    MidColor = {r=54, g=140, b=253},
                    BottomColor = {r=71, g=169, b=247},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=71, g=169, b=247},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=107, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=23, g=64, b=154},
                    MidColor = {r=211, g=155, b=252},
                    BottomColor = {r=255, g=151, b=182},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=255, g=151, b=182},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=67, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=4, g=27, b=60},
                    MidColor = {r=26, g=62, b=98},
                    BottomColor = {r=44, g=87, b=156},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.7,
                },
                Fog = {
                    Enable = true,
                    Color = {r=44, g=87, b=156},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=0, g=0, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=17, g=32, b=64},
                    MidColor = {r=0, g=39, b=75},
                    BottomColor = {r=35, g=60, b=104},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=35, g=60, b=104},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=21, g=21, b=21},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=12, g=19, b=67},
                    MidColor = {r=83, g=37, b=164},
                    BottomColor = {r=55, g=92, b=134},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=55, g=92, b=134},
                    RangeStart = 0,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=0, g=0, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=25, g=153, b=102, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 草原
    [24] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_grassland",
        Name = GetS(301353),
        TemplateIdx = 24,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=0, g=91, b=144},
                    MidColor = {r=133, g=215, b=248},
                    BottomColor = {r=228, g=215, b=181},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.05,
                },
                Fog = {
                    Enable = true,
                    Color = {r=228, g=215, b=181},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=238, g=81, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=21, g=116, b=185},
                    MidColor = {r=70, g=187, b=255},
                    BottomColor = {r=76, g=185, b=197},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=76, g=185, b=197},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=180, g=140, b=61},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=0, g=106, b=192},
                    MidColor = {r=89, g=217, b=255},
                    BottomColor = {r=212, g=230, b=179},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=212, g=230, b=179},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=195, b=138},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=30, g=130, b=217},
                    MidColor = {r=123, g=254, b=249},
                    BottomColor = {r=189, g=204, b=137},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=189, g=204, b=137},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=161, g=87, b=23},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=0, g=212, b=255},
                    MidColor = {r=255, g=249, b=151},
                    BottomColor = {r=253, g=209, b=140},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=253, g=209, b=140},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=103, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=6, g=25, b=60},
                    MidColor = {r=0, g=27, b=52},
                    BottomColor = {r=0, g=153, b=234},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=0, g=153, b=234},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=83, g=66, b=91},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=0, g=12, b=19},
                    MidColor = {r=17, g=65, b=90},
                    BottomColor = {r=10, g=56, b=95},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=10, g=56, b=95},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=29, g=79, b=133},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=9, g=27, b=80},
                    MidColor = {r=23, g=53, b=143},
                    BottomColor = {r=112, g=121, b=248},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=112, g=121, b=248},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=115, g=109, b=182},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=76, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 热带草原
    [25] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_arid",
        Name = GetS(301354),
        TemplateIdx = 25,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=159, g=172, b=217},
                    MidColor = {r=216, g=224, b=247},
                    BottomColor = {r=198, g=131, b=122},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=198, g=131, b=122},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=221, g=123, b=30},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=110, g=141, b=167},
                    MidColor = {r=224, g=210, b=197},
                    BottomColor = {r=233, g=201, b=160},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=233, g=201, b=160},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=144, g=122, b=97},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=105, g=170, b=224},
                    MidColor = {r=108, g=179, b=225},
                    BottomColor = {r=170, g=214, b=225},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=170, g=214, b=225},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=156, g=144, b=134},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=106, g=96, b=122},
                    MidColor = {r=170, g=170, b=179},
                    BottomColor = {r=219, g=191, b=159},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=219, g=191, b=159},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=161, g=87, b=23},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=68, g=73, b=149},
                    MidColor = {r=253, g=177, b=99},
                    BottomColor = {r=162, g=148, b=159},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=162, g=148, b=159},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=103, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=16, g=36, b=68},
                    MidColor = {r=105, g=93, b=125},
                    BottomColor = {r=143, g=123, b=134},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.5,
                },
                Fog = {
                    Enable = true,
                    Color = {r=143, g=123, b=134},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=83, g=66, b=91},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=41, g=47, b=56},
                    MidColor = {r=35, g=27, b=96},
                    BottomColor = {r=53, g=78, b=176},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.8,
                },
                Fog = {
                    Enable = true,
                    Color = {r=53, g=78, b=176},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=175, g=172, b=172},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=15, g=36, b=63},
                    MidColor = {r=75, g=83, b=119},
                    BottomColor = {r=125, g=142, b=166},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=125, g=142, b=166},
                    RangeStart = 0,
                    RangeEnd = 130,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=115, g=109, b=182},
                },
                Water = {
                    Enable = true,
                    Color = {r=51, g=331, b=280, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
    -- 发光空岛
    [26] = {
        iconPath = "ui://ugc_resourcesidebar/icon_sky_glowingland",
        Name = GetS(301355),
        TemplateIdx = 26,
        SkyTex = {},
        SunTex = {},
        MoonTex = {},
        IsScroll = true,

        TimeNode = {
            [1] = {
                Sky = {
                    TopColor = {r=114, g=111, b=146},
                    MidColor = {r=161, g=155, b=157},
                    BottomColor = {r=235, g=170, b=127},
                    SkyLightColor = {r=242.25, g=147.89999999999998, b=73.94999999999999},
                    AmbientColor = {r=15.299999999999999, g=33.15, b=86.7},
                },
                Sun = {
                    Color = {r=229.5, g=102.0, b=51.0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.02,
                },
                Fog = {
                    Enable = true,
                    Color = {r=235, g=170, b=127},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=255, g=118, b=0},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [2] = {
                Sky = {
                    TopColor = {r=101, g=123, b=197},
                    MidColor = {r=186, g=187, b=210},
                    BottomColor = {r=234, g=200, b=176},
                    SkyLightColor = {r=252.45, g=193.8, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=244.79999999999998, g=175.95, b=114.75},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=234, g=200, b=176},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=158, g=152, b=139},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [3] = {
                Sky = {
                    TopColor = {r=59, g=114, b=221},
                    MidColor = {r=152, g=197, b=239},
                    BottomColor = {r=255, g=252, b=236},
                    SkyLightColor = {r=252.45, g=226.95000000000002, b=114.75},
                    AmbientColor = {r=22.95, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=237.15, b=160.65},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=255, g=252, b=236},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=120, g=133, b=146},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [4] = {
                Sky = {
                    TopColor = {r=60, g=125, b=170},
                    MidColor = {r=204, g=235, b=238},
                    BottomColor = {r=232, g=238, b=220},
                    SkyLightColor = {r=252.45, g=196.35, b=99.45},
                    AmbientColor = {r=17.85, g=17.85, b=68.85000000000001},
                },
                Sun = {
                    Color = {r=249.9, g=198.9, b=109.64999999999999},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.001,
                },
                Fog = {
                    Enable = true,
                    Color = {r=232, g=238, b=220},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=226, g=141, b=107},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [5] = {
                Sky = {
                    TopColor = {r=0, g=15, b=81},
                    MidColor = {r=87, g=83, b=163},
                    BottomColor = {r=242, g=158, b=124},
                    SkyLightColor = {r=232.05, g=119.85, b=71.4},
                    AmbientColor = {r=25.5, g=22.95, b=53.55},
                },
                Sun = {
                    Color = {r=252.45, g=168.3, b=132.6},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.1,
                },
                Fog = {
                    Enable = true,
                    Color = {r=242, g=158, b=124},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=141, g=35, b=38},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [6] = {
                Sky = {
                    TopColor = {r=0, g=7, b=26},
                    MidColor = {r=28, g=70, b=162},
                    BottomColor = {r=116, g=91, b=201},
                    SkyLightColor = {r=35, g=42, b=71},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=53.55, g=68.85000000000001, b=112.2},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=206.55, g=175.95, b=211.64999999999998},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.6,
                },
                Fog = {
                    Enable = true,
                    Color = {r=116, g=91, b=201},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=145, g=173, b=202},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [7] = {
                Sky = {
                    TopColor = {r=0, g=7, b=33},
                    MidColor = {r=6, g=34, b=95},
                    BottomColor = {r=9, g=99, b=156},
                    SkyLightColor = {r=7, g=8, b=15},
                    AmbientColor = {r=11, g=15, b=33},
                },
                Sun = {
                    Color = {r=0, g=0, b=0},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=186.15, g=219.29999999999998, b=232.05},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.85,
                },
                Fog = {
                    Enable = true,
                    Color = {r=9, g=99, b=156},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=209, g=14, b=189},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
            [8] = {
                Sky = {
                    TopColor = {r=6, g=19, b=33},
                    MidColor = {r=18, g=62, b=83},
                    BottomColor = {r=31, g=135, b=109},
                    SkyLightColor = {r=10, g=15, b=20},
                    AmbientColor = {r=30, g=35, b=48},
                },
                Sun = {
                    Color = {r=124.95, g=61.199999999999996, b=25.5},
                    Width = 0.7,
                },
                Moon = {
                    Color = {r=193.8, g=186.15, b=193.8},
                    Width = 0.7,
                },
                Star = {
                    Density = 0.2,
                },
                Fog = {
                    Enable = true,
                    Color = {r=31, g=135, b=109},
                    RangeStart = -10,
                    RangeEnd = 120,
                },
                Cloud = {
                    Speed = 1,
                    Density = 2.1,
                    Height = 20,
                    Color = {r=89, g=254, b=242},
                },
                Water = {
                    Enable = true,
                    Color = {r=127, g=510, b=816, a=224},
                    ReflectStrength = 100,
                },
                Wind = {
                    Speed = 28,
                },
            },
        },
    },
}

-- 后处理模板参数
local PostProcessTemplate = {
    [1] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_def",
        Name = GetS(301258),
        TemplateIdx = 1,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=0,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },    
    [2] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_sunday",
        Name = GetS(301276),
        TemplateIdx = 2,
        ColorGrading = {
            Gain = { b = 199, g = 226, r = 229, },
            Contrast = 40,
            Saturation = 50,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 20,
        },
        Exposure = {
            Intensity = 60,
        },
        Gamma = {
            Intensity = 50,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 14,
            Color = {r=255, g=255, b=255,},
        },
    },
    [3] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_dim",
        Name = GetS(301277),
        TemplateIdx = 3,
        ColorGrading = {
            Gain = { b = 255, g = 255, r = 255, },
            Contrast = 50,
            Saturation = 50,
            LutTex = 4,
        },
        Bloom = {
            Intensity = 0,
        },
        Exposure = {
            Intensity = 50,
        },
        Gamma = {
            Intensity = 50,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
            Color = {r=255, g=255, b=255,},
        },
    },
    [4] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_mountain",
        Name = GetS(301283),
        TemplateIdx = 4,
        ColorGrading = {
            Gain = { b = 255, g = 251, r = 135, },
            Contrast = 53,
            Saturation = 47,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 12,
        },
        Exposure = {
            Intensity = 63,
        },
        Gamma = {
            Intensity = 51,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
            Color = {r=255, g=255, b=255,},
        },
    },
    [5] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_cool",
        Name = GetS(301279),
        TemplateIdx = 5,
        ColorGrading = {
            Gain = { b = 239, g = 125, r = 83, },
            Contrast = 61,
            Saturation = 39,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 9,
        },
        Exposure = {
            Intensity = 100,
        },
        Gamma = {
            Intensity = 73,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
            Color = {r=255, g=255, b=255,},
        },
    },
    [6] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_blood",
        Name = GetS(301280),
        TemplateIdx = 6,
        ColorGrading = {
            Gain = { b = 61, g = 61, r = 255, },
            Contrast = 82,
            Saturation = 0,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 11,
        },
        Exposure = {
            Intensity = 68,
        },
        Gamma = {
            Intensity = 96,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
            Color = {r=255, g=255, b=255,},
        },
    },
    [7] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_morning",
        Name = GetS(301278),
        TemplateIdx = 7,
        ColorGrading = {
            Gain = { b = 103, g = 230, r = 252, },
            Contrast = 56,
            Saturation = 56,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 14,
        },
        Exposure = {
            Intensity = 72,
        },
        Gamma = {
            Intensity = 53,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
            Color = {r=255, g=255, b=255,},
        },
    },
    [8] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_old",
        Name = GetS(301281),
        TemplateIdx = 8,
        ColorGrading = {
            Gain = { b = 128, g = 134, r = 142, },
            Contrast = 92,
            Saturation = 30,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 11,
        },
        Exposure = {
            Intensity = 90,
        },
        Gamma = {
            Intensity = 78,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
            Color = {r=255, g=255, b=255,},
        },
    },
    [9] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_gray",
        Name = GetS(301282),
        TemplateIdx = 9,
        ColorGrading = {
            Gain = { b = 165, g = 165, r = 165, },
            Contrast = 72,
            Saturation = 0,
            LutTex = 0,
        },
        Bloom = {
            Intensity = 0,
        },
        Exposure = {
            Intensity = 96,
        },
        Gamma = {
            Intensity = 86,
        },
        Dof = {
            Enable = false,
        },
        GodRay = {
            Intensity = 0,
        },
    },
    -- 电影感
    [10] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_cinematic",
        Name = GetS(301380),
        TemplateIdx = 10,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=6,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 海岛
    [11] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_island",
        Name = GetS(301381),
        TemplateIdx = 11,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=7,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 回忆
    [12] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_memory",
        Name = GetS(301382),
        TemplateIdx = 12,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=8,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 明艳
    [13] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_bright",
        Name = GetS(301383),
        TemplateIdx = 13,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=9,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 青橙
    [14] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_tealorange",
        Name = GetS(301384),
        TemplateIdx = 14,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=10,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 清新
    [15] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_fresh",
        Name = GetS(301385),
        TemplateIdx = 15,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=11,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 日系
    [16] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_japanese_style",
        Name = GetS(301386),
        TemplateIdx = 16,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=12,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 日系过曝
    [17] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_overexposed_japanese_style",
        Name = GetS(301387),
        TemplateIdx = 17,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=13,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
    -- 郁金香
    [18] = {
        iconPath = "ui://ugc_resourcesidebar/icon_popTemp_tulip",
        Name = GetS(301388),
        TemplateIdx = 18,
        ColorGrading = {
            Gain={r = 255, g = 255, b = 255},
            Contrast=50,
            Saturation=50,
            LutTex=14,
        },
    
        Bloom = {
            Intensity=15,
        },
    
        Exposure = {
            Intensity=50,
        },
    
        Gamma = {
            Intensity=50,
        },
    
        Dof = {
            Enable = false,
        },
    
        GodRay = {
            Intensity=22.5,
            Color = {r=255, g=255, b=255,},
        },
    },
}

local skyTexTemplate = {
    {iconPath = "ui://ugc_resourcesidebar/icon_sky_1", texPath = "ugcenv/skyCube1.png", isCubeTex = true},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_2", texPath = "ugcenv/skyCube2.png", isCubeTex = true},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_ink", texPath = "ugcenv/shuimo.png", isCubeTex = true},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_inkGreen", texPath = "ugcenv/shuimo2.png", isCubeTex = true},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_9", texPath = "ugcenv/skyCube3.png", isCubeTex = true},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_10", texPath = "ugcenv/skyCube4.png", isCubeTex = true},
    {iconPath = "ui://ugc_resourcesidebar/icon_sky_3", texPath = "ugcenv/sky1.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_4", texPath = "ugcenv/sky2.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_5", texPath = "ugcenv/sky3.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_6", texPath = "ugcenv/sky5.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_7", texPath = "ugcenv/sky6.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sky_8", texPath = "ugcenv/sky7.png"},
}

local SunTexTemplate = {
    {iconPath = "ui://ugc_resourcesidebar/icon_sun_1", texPath = "ugcenv/sun1.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sun_2", texPath = "ugcenv/sun2.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sun_3", texPath = "ugcenv/sun3.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sun_4", texPath = "ugcenv/sun4.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_sun_5", texPath = "ugcenv/sun5.png"},
}

local MoonTexTemplate = {
    {iconPath = "ui://ugc_resourcesidebar/icon_moon_1", texPath = "ugcenv/moon1.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_moon_2", texPath = "ugcenv/moon2.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_moon_3", texPath = "ugcenv/moon3.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_moon_4", texPath = "ugcenv/moon4.png"},
    -- {iconPath = "ui://ugc_resourcesidebar/icon_moon_5", texPath = "ugcenv/moon5.png"},
}

local LutTexDefault = {
    iconPath = "ui://ugc_resourcesidebar/icon_lut_0", texPath = "", Name = GetS(301259)
}

local LutTexTemplate = {
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_1", texPath = "ugcenv/lut1.png", Name = GetS(301271)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_2", texPath = "ugcenv/lut2.png", Name = GetS(301272)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_3", texPath = "ugcenv/lut3.png", Name = GetS(301273)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_4", texPath = "ugcenv/lut4.png", Name = GetS(301274)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_5", texPath = "ugcenv/lut5.png", Name = GetS(301275)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_6", texPath = "ugcenv/lut6.png", Name = GetS(301380)}, --电影感
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_7", texPath = "ugcenv/lut7.png", Name = GetS(301381)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_8", texPath = "ugcenv/lut8.png", Name = GetS(301382)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_9", texPath = "ugcenv/lut9.png", Name = GetS(301383)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_10", texPath = "ugcenv/lut10.png", Name = GetS(301384)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_11", texPath = "ugcenv/lut11.png", Name = GetS(301385)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_12", texPath = "ugcenv/lut12.png", Name = GetS(301386)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_13", texPath = "ugcenv/lut13.png", Name = GetS(301387)},
    {iconPath = "ui://ugc_resourcesidebar/icon_lut_14", texPath = "ugcenv/lut14.png", Name = GetS(301388)},
}

_G.EnvSetData = {
    datakey = 'envSet',
    m_data = MapEnvSet,
    m_needsave = false,
    m_owid = 0,
    curTimeIx = 1,
    timeNodeTimes = {
        6, 8, 12, 16, 18, 20, 24, 4
    },
}

EnvSetData.GetSkyTemplates = function(self)
    return SkyTemplate
end

EnvSetData.GetSkyTemplateNum = function()
    return #SkyTemplate
end

EnvSetData.GetSkyTemplateIcon = function(self, templateIdx)
    if templateIdx > 0 and templateIdx <= #SkyTemplate then
        return SkyTemplate[templateIdx].iconPath
    else
        return nil
    end
end

EnvSetData.GetSkyTemplateName = function(self, templateIdx)
    if not templateIdx then
        return nil
    end

    if self.m_data.SkyBox and self.m_data.SkyBox[templateIdx] then
        return self.m_data.SkyBox[templateIdx].Name
    elseif templateIdx > 0 and templateIdx <= #SkyTemplate then
        return SkyTemplate[templateIdx].Name
    else
        return nil
    end
end

EnvSetData.GetSkyCurTemplateName = function(self)
    if self.curSkyBox then
        return self.curSkyBox.Name
    end

    return ""
end

EnvSetData.SetSkyCurTemplateName = function (self,value)
    if self.curSkyBox then
        self.curSkyBox.Name = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcessTemplates = function(self)
    return PostProcessTemplate
end

EnvSetData.GetPostProcessTemplateNum = function()
    return #PostProcessTemplate
end

EnvSetData.GetPostProcessTemplateIcon = function(self, templateIdx)
    if templateIdx > 0 and templateIdx <= #PostProcessTemplate then
        return PostProcessTemplate[templateIdx].iconPath
    else
        return nil
    end
end

EnvSetData.GetPostProcessTemplateName = function(self, templateIdx)
    if not templateIdx then
        return nil
    end
    
    if self.m_data.PostProcesss and self.m_data.PostProcesss[templateIdx] then
        return self.m_data.PostProcesss[templateIdx].Name
    elseif templateIdx > 0 and templateIdx <= #PostProcessTemplate then
        return PostProcessTemplate[templateIdx].Name
    else
        return nil
    end
end

EnvSetData.GetPostProcessCurTemplateName = function(self)
    if self.curPostProcess then
        return self.curPostProcess.Name
    end

    return ""
end

EnvSetData.SetPostProcessCurTemplateName = function (self,value)
    if self.curPostProcess then
        self.curPostProcess.Name = value
        self.m_needsave = true
    end
end


EnvSetData.GetSkyTexTemplates = function(self)
    return skyTexTemplate
end

EnvSetData.GetSunTexTemplates = function(self)
    return SunTexTemplate
end

EnvSetData.GetMoonTexTemplates = function(self)
    return MoonTexTemplate
end

EnvSetData.GetLutTexTemplates = function(self)
    return LutTexTemplate
end

EnvSetData.GetLutTexDefaultTemplate = function(self)
    return LutTexDefault
end

EnvSetData.GetSkyTexTemplateByIndex = function(self, index)
    return self:GetTexTemplate(skyTexTemplate, self:GetSkyTex(), index)
end

EnvSetData.GetSunTexTemplateByIndex = function(self, index)
    return self:GetTexTemplate(SunTexTemplate, self:GetSunTex(), index)
end

EnvSetData.GetMoonTexTemplateByIndex = function(self, index)
    return self:GetTexTemplate(MoonTexTemplate, self:GetMoonTex(), index)
end

EnvSetData.GetSkyTexTemplateIndexByPath = function(self, path)
    return self:GetTexTemplateIndex(skyTexTemplate, path)
end

EnvSetData.GetSkyTexTemplateByPath = function(self, path)
    return self:GetTexTemplateByPath(skyTexTemplate, path)
end

EnvSetData.GetSunTexTemplateIndexByPath = function(self, path)
    return self:GetTexTemplateIndex(SunTexTemplate, path)
end

EnvSetData.GetMoonTexTemplateIndexByPath = function(self, path)
    return self:GetTexTemplateIndex(MoonTexTemplate, path)
end

EnvSetData.GetLutTexTemplateByIndex = function(self, index)
    return self:GetTexTemplate(LutTexTemplate, self:GetPostProcesssLut(), index)
end

EnvSetData.GetLutTexTemplateName = function(self, templateIdx)
    if not templateIdx then
        return nil
    end

    local template = self:GetLutTexTemplateByIndex(templateIdx)
    if template then
        return template.Name
    end

    return ""
end

EnvSetData.GetTexTemplate = function(self, templates, texInfo, index)
    if index then
        if index > 0 and index <= #templates then
            return templates[index]
        end
    else
        if texInfo then
            if type(texInfo) == "number" then
                return templates[texInfo]
            elseif type(texInfo) == "table" and texInfo.idx then
                return templates[texInfo.idx]
            end
        end
    end

    return nil
end

EnvSetData.GetTexTemplateIndex = function(self, templates, path)
    if path then
        for i = 1, #templates do
            if templates[i].texPath == path then
                return i
            end
        end
    end

    return 0
end

EnvSetData.GetTexTemplateByPath = function(self, templates, path)
    if path then
        for i = 1, #templates do
            if templates[i].texPath == path then
                return templates[i]
            end
        end
    end

    return nil
end

EnvSetData.GetTimeNodeTimes = function (self)
    return self.timeNodeTimes
end

EnvSetData.GetTimePassing = function (self)
    return self.m_data.TimePassing
end

EnvSetData.SetTimePassing = function (self,value)
    self.m_data.TimePassing = value
    self.m_needsave = true
end

EnvSetData.IsSkyTemplateNotModify = function (self)
    if self.curSkyBox then
        return not self.curSkyBox.modify
    end

    return false
end

EnvSetData.IsPostProcessTemplateNotModify = function (self)
    if self.curPostProcess then
        return not self.curPostProcess.modify
    end

    return false
end

EnvSetData.IsDefaultSky = function(self, idx)
    if idx  then
        return idx == 1
    elseif not idx and self.curSkyBox then
        return self.curSkyBox.TemplateIdx == 1
    end

    return false
end

EnvSetData.IsCustomSky = function (self, idx)
    if idx and self.m_data.SkyBox[idx] then
        return self.m_data.SkyBox[idx].modify
    elseif not idx and self.curSkyBox then
        return self.curSkyBox.modify
    end

    return false
end

EnvSetData.SetCustomSky = function (self)
    if self.curSkyBox then
        self.curSkyBox.modify = true
    end

    return false
end

EnvSetData.GetSkyTemplateIdx = function (self)
    return self.m_data.curSkyTemplateIdx
end

EnvSetData.IsCustomPostProcess = function (self, idx)
    if idx and self.m_data.PostProcesss[idx] then
        return self.m_data.PostProcesss[idx].modify
    elseif not idx and self.curPostProcess then
        return self.curPostProcess.modify
    end

    return false
end

EnvSetData.IsDefaultPostProcess = function(self, idx)
    if idx  then
        return idx == 1
    elseif not idx and self.curPostProcess then
        return self.curPostProcess.TemplateIdx == 1
    end

    return false
end

EnvSetData.SetCustomPostProcess = function (self)
    if self.curPostProcess then
        self.curPostProcess.modify = true
    end

    return false
end

EnvSetData.GetPostProcessTemplateIdx = function (self)
    return self.m_data.curPostProcessTemplateIdx
end

EnvSetData.SetSkyTemplateIdx = function (self, value, disableSaveData)
    if self.m_data.SkyBox then
        -- 已有的，数据不需要覆盖
        if self.m_data.SkyBox[value] then
            self.curSkyBox = self.m_data.SkyBox[value]
            self.m_data.curSkyTemplateIdx = value
            if self.curSkyBox and self.curSkyBox.TimeNode then
                self.curSkyTimeNode = self.curSkyBox.TimeNode
            end
        else
            if value > 0 then
                local realI = value
                if realI > 0 and realI <= #SkyTemplate then
                    self.m_data.SkyBox[value] = copy_table(SkyTemplate[realI])
                end
            end

            self.m_data.curSkyTemplateIdx = value
            if self.m_data.SkyBox[value] and self.m_data.SkyBox[value].TimeNode then
                self.curSkyTimeNode = self.m_data.SkyBox[value].TimeNode
            end
            if self.m_data.SkyBox[value] then
                self.curSkyBox = self.m_data.SkyBox[value]
            end
        end

        if not disableSaveData then
            self.m_needsave = true
        end
    end
end

-- 重置
EnvSetData.RestoreSkyTemplate = function (self)
    if self.m_data.SkyBox then
        if self.m_data.curSkyTemplateIdx > 0 then
            local realI = self.m_data.curSkyTemplateIdx
            if realI > 0 and realI <= #SkyTemplate then
                self.m_data.SkyBox[self.m_data.curSkyTemplateIdx] = copy_table(SkyTemplate[realI])
            end
        end

        if self.m_data.SkyBox and self.m_data.SkyBox[self.m_data.curSkyTemplateIdx] then
            self.curSkyBox = self.m_data.SkyBox[self.m_data.curSkyTemplateIdx]
        end
    
        if  self.curSkyBox and  self.curSkyBox.TimeNode then
            self.curSkyTimeNode =  self.curSkyBox.TimeNode
        end

        self.m_needsave = true
    end
end


EnvSetData.SetPostProcessTemplateIdx = function (self, value, disableSaveData)
    if self.m_data.PostProcesss then
        -- 已有的，数据不需要覆盖
        if self.m_data.PostProcesss[value] then
            self.curPostProcess = self.m_data.PostProcesss[value]
            self.m_data.curPostProcessTemplateIdx = value
        else
            -- 从第二个元素开始才是自定义
            if value > 0 then
                local realI = value
                if realI > 0 and realI <= #PostProcessTemplate then
                    self.m_data.PostProcesss[value] = copy_table(PostProcessTemplate[realI])
                end
            end

            self.m_data.curPostProcessTemplateIdx = value
            if self.m_data.PostProcesss[value] then
                self.curPostProcess = self.m_data.PostProcesss[value]
            end
        end

        if not disableSaveData then
            self.m_needsave = true
        end
    end
end

-- 重置
EnvSetData.RestorePostProcessTemplate = function (self)
    if self.m_data.PostProcesss then
        if self.m_data.curPostProcessTemplateIdx > 0 then
            local realI = self.m_data.curPostProcessTemplateIdx
            if realI > 0 and realI <= #PostProcessTemplate then
                self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx] = copy_table(PostProcessTemplate[realI])
            end
        end

        if self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx] then
            self.curPostProcess = self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx]
        end

        self.m_needsave = true
    end
end

EnvSetData.GetSkyTex = function (self)
    if self.curSkyBox then
        return self.curSkyBox.SkyTex
    end
    return nil
end

EnvSetData.SetSkyTex = function (self,value)
    if self.curSkyBox then
        self.curSkyBox.SkyTex = value
        self.m_needsave = true
    end
end

EnvSetData.GetSunTex = function (self)
    if self.curSkyBox then
        return self.curSkyBox.SunTex
    end
    return nil
end

EnvSetData.SetSunTex = function (self,value)
    if self.curSkyBox then
        self.curSkyBox.SunTex = value
        self.m_needsave = true
    end
end

EnvSetData.GetMoonTex = function (self)
    if self.curSkyBox then
        return self.curSkyBox.MoonTex
    end
    return nil
end

EnvSetData.SetMoonTex = function (self,value)
    if self.curSkyBox then
        self.curSkyBox.MoonTex = value
        self.m_needsave = true
    end
end

EnvSetData.GetIsScroll = function (self)
    if self.curSkyBox then
        return self.curSkyBox.IsScroll
    end
    return true
end

EnvSetData.SetIsScroll = function (self,value)
    if self.curSkyBox then
        self.curSkyBox.IsScroll = value
        self.m_needsave = true
    end
end

EnvSetData.GetCurSkyBoxIx = function (self)
    return self.curTimeIx
end

EnvSetData.SetCurSkyBoxIx = function (self, ix)
    self.curTimeIx = ix
end

EnvSetData.GetSkyLightColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sky then
        return self.curSkyTimeNode[self.curTimeIx].Sky.SkyLightColor
    end
    return nil
end

EnvSetData.SetSkyLightColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sky then
            self.curSkyTimeNode[self.curTimeIx].Sky = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sky.SkyLightColor = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyAmbientColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sky then
        return self.curSkyTimeNode[self.curTimeIx].Sky.AmbientColor
    end
    return nil
end

EnvSetData.SetSkyAmbientColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sky then
            self.curSkyTimeNode[self.curTimeIx].Sky = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sky.AmbientColor = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyTopColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sky then
        return self.curSkyTimeNode[self.curTimeIx].Sky.TopColor
    end
    return nil
end

EnvSetData.SetSkyTopColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sky then
            self.curSkyTimeNode[self.curTimeIx].Sky = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sky.TopColor = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyMidColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sky then
        return self.curSkyTimeNode[self.curTimeIx].Sky.MidColor
    end
    return nil
end

EnvSetData.SetSkyMidColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sky then
            self.curSkyTimeNode[self.curTimeIx].Sky = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sky.MidColor = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyBottomColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sky then
        return self.curSkyTimeNode[self.curTimeIx].Sky.BottomColor
    end
    return nil
end

EnvSetData.SetSkyBottomColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sky then
            self.curSkyTimeNode[self.curTimeIx].Sky = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sky.BottomColor = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkySunColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sun then
        return self.curSkyTimeNode[self.curTimeIx].Sun.Color
    end
    return nil
end

EnvSetData.SetSkySunColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sun then
            self.curSkyTimeNode[self.curTimeIx].Sun = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sun.Color = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkySunWidth = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Sun then
        return self.curSkyTimeNode[self.curTimeIx].Sun.Width
    end
    return nil
end

EnvSetData.SetSkySunWidth = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Sun then
            self.curSkyTimeNode[self.curTimeIx].Sun = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Sun.Width = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyMoonColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Moon then
        return self.curSkyTimeNode[self.curTimeIx].Moon.Color
    end
    return nil
end

EnvSetData.SetSkyMoonColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Moon then
            self.curSkyTimeNode[self.curTimeIx].Moon = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Moon.Color = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyMoonWidth = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Moon then
        return self.curSkyTimeNode[self.curTimeIx].Moon.Width
    end
    return nil
end

EnvSetData.SetSkyMoonWidth = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Moon then
            self.curSkyTimeNode[self.curTimeIx].Moon = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Moon.Width = value
        self.m_needsave = true
    end
end

EnvSetData.GetSkyStarDensity = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Star then
        return self.curSkyTimeNode[self.curTimeIx].Star.Density
    end
    return nil
end

EnvSetData.SetSkyStarDensity = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Star then
            self.curSkyTimeNode[self.curTimeIx].Star = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Star.Density = value
        self.m_needsave = true
    end
end

EnvSetData.GetFogEnable = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Fog then
        return self.curSkyTimeNode[self.curTimeIx].Fog.Enable
    end
    return nil
end

EnvSetData.SetFogEnable = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Fog then
            self.curSkyTimeNode[self.curTimeIx].Fog = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Fog.Enable = value
        self.m_needsave = true
    end
end

EnvSetData.GetFogRangeMin = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Fog then
        return self.curSkyTimeNode[self.curTimeIx].Fog.RangeStart
    end
    return nil
end

EnvSetData.SetFogRangeMin = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Fog then
            self.curSkyTimeNode[self.curTimeIx].Fog = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Fog.RangeStart = value
        self.m_needsave = true
    end
end

EnvSetData.GetFogRangeMax = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Fog then
        return self.curSkyTimeNode[self.curTimeIx].Fog.RangeEnd
    end
    return nil
end

EnvSetData.SetFogRangeMax = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Fog then
            self.curSkyTimeNode[self.curTimeIx].Fog = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Fog.RangeEnd = value
        self.m_needsave = true
    end
end

EnvSetData.GetFogColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Fog then
        return self.curSkyTimeNode[self.curTimeIx].Fog.Color
    end
    return nil
end

EnvSetData.SetFogColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Fog then
            self.curSkyTimeNode[self.curTimeIx].Fog = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Fog.Color = value
        self.m_needsave = true
    end
end

EnvSetData.GetCloudSpeed = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Cloud then
        return self.curSkyTimeNode[self.curTimeIx].Cloud.Speed
    end
    return nil
end

EnvSetData.SetCloudSpeed = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Cloud then
            self.curSkyTimeNode[self.curTimeIx].Cloud = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Cloud.Speed = value
        self.m_needsave = true
    end
end

EnvSetData.GetCloudDensity = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Cloud then
        return self.curSkyTimeNode[self.curTimeIx].Cloud.Density
    end
    return nil
end

EnvSetData.SetCloudDensity = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Cloud then
            self.curSkyTimeNode[self.curTimeIx].Cloud = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Cloud.Density = value
        self.m_needsave = true
    end
end

EnvSetData.GetCloudColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Cloud then
        return self.curSkyTimeNode[self.curTimeIx].Cloud.Color
    end
    return nil
end

EnvSetData.SetCloudColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Cloud then
            self.curSkyTimeNode[self.curTimeIx].Cloud = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Cloud.Color = value
        self.m_needsave = true
    end
end

EnvSetData.GetCloudHeight = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Cloud then
        return self.curSkyTimeNode[self.curTimeIx].Cloud.Height
    end
    return nil
end

EnvSetData.SetCloudHeight = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Cloud then
            self.curSkyTimeNode[self.curTimeIx].Cloud = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Cloud.Height = value
        self.m_needsave = true
    end
end

EnvSetData.GetWaterEnable = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Water then
        return self.curSkyTimeNode[self.curTimeIx].Water.Enable
    end
    return nil
end

EnvSetData.SetWaterEnable = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Water then
            self.curSkyTimeNode[self.curTimeIx].Water = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Water.Enable = value
        self.m_needsave = true
    end
end

EnvSetData.GetWaterColor = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Water then
        return self.curSkyTimeNode[self.curTimeIx].Water.Color
    end
    return nil
end

EnvSetData.SetWaterColor = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Water then
            self.curSkyTimeNode[self.curTimeIx].Water = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Water.Color = value
        self.m_needsave = true
    end
end

EnvSetData.GetWaterReflectStrength = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Water then
        return self.curSkyTimeNode[self.curTimeIx].Water.ReflectStrength
    end
    return nil
end

EnvSetData.SetWaterReflectStrength = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Water then
            self.curSkyTimeNode[self.curTimeIx].Water = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Water.ReflectStrength = value
        self.m_needsave = true
    end
end

EnvSetData.GetWindStrength = function (self)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] and self.curSkyTimeNode[self.curTimeIx].Wind then
        return self.curSkyTimeNode[self.curTimeIx].Wind.Strength
    end
    return nil
end

EnvSetData.SetWindStrength = function (self,value)
    if self.curSkyTimeNode and self.curSkyTimeNode[self.curTimeIx] then
        if not self.curSkyTimeNode[self.curTimeIx].Wind then
            self.curSkyTimeNode[self.curTimeIx].Wind = {}
        end
        
        self.curSkyTimeNode[self.curTimeIx].Wind.Strength = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssGain = function (self)
    if self.curPostProcess and self.curPostProcess.ColorGrading then
        return self.curPostProcess.ColorGrading.Gain
    end
    return nil
end

EnvSetData.SetPostProcesssGain = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.ColorGrading then
            self.curPostProcess.ColorGrading = {}
        end
        
        self.curPostProcess.ColorGrading.Gain = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssContrast = function (self)
    if self.curPostProcess and self.curPostProcess.ColorGrading then
        return self.curPostProcess.ColorGrading.Contrast
    end
    return nil
end

EnvSetData.SetPostProcesssContrast = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.ColorGrading then
            self.curPostProcess.ColorGrading = {}
        end
        
        self.curPostProcess.ColorGrading.Contrast = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssSaturation = function (self)
    if self.curPostProcess and self.curPostProcess.ColorGrading then
        return self.curPostProcess.ColorGrading.Saturation
    end
    return nil
end

EnvSetData.SetPostProcesssSaturation = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.ColorGrading then
            self.curPostProcess.ColorGrading = {}
        end
        
        self.curPostProcess.ColorGrading.Saturation = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssLut = function (self)
    if self.curPostProcess and self.curPostProcess.ColorGrading then
        return self.curPostProcess.ColorGrading.LutTex
    end
    return nil
end

EnvSetData.SetPostProcesssLut = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.ColorGrading then
            self.curPostProcess.ColorGrading = {}
        end
        
        self.curPostProcess.ColorGrading.LutTex = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssBloomIntensity = function (self)
    if self.curPostProcess and self.curPostProcess.Bloom then
        return self.curPostProcess.Bloom.Intensity
    end
    return nil
end

EnvSetData.SetPostProcesssBloomIntensity = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.Bloom then
            self.curPostProcess.Bloom = {}
        end
        
        self.curPostProcess.Bloom.Intensity = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssExposureIntensity = function (self)
    if self.curPostProcess and self.curPostProcess.Exposure then
        return self.curPostProcess.Exposure.Intensity
    end
    return nil
end

EnvSetData.SetPostProcesssExposureIntensity = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.Exposure then
            self.curPostProcess.Exposure = {}
        end
        
        self.curPostProcess.Exposure.Intensity = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssGammaIntensity = function (self)
    if self.curPostProcess and self.curPostProcess.Gamma then
        return self.curPostProcess.Gamma.Intensity
    end
    return nil
end

EnvSetData.SetPostProcesssGammaIntensity = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.Gamma then
            self.curPostProcess.Gamma = {}
        end
        
        self.curPostProcess.Gamma.Intensity = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssDofEnable = function (self)
    if self.curPostProcess and self.curPostProcess.Dof then
        return self.curPostProcess.Dof.Enable
    end
    return nil
end

EnvSetData.SetPostProcesssDofEnable = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.Dof then
            self.curPostProcess.Dof = {}
        end
        
        self.curPostProcess.Dof.Enable = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssGodRayIntensity = function (self)
    if self.curPostProcess and self.curPostProcess.GodRay then
        return self.curPostProcess.GodRay.Intensity
    end
    return nil
end

EnvSetData.SetPostProcesssGodRayIntensity = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.GodRay then
            self.curPostProcess.GodRay = {}
        end
        
        self.curPostProcess.GodRay.Intensity = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssGodRayColor = function (self,value)
    if self.curPostProcess and self.curPostProcess.GodRay then
        return self.curPostProcess.GodRay.Color
    end
    return nil
end

EnvSetData.SetPostProcesssGodRayColor = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.GodRay then
            self.curPostProcess.GodRay = {}
        end
        
        self.curPostProcess.GodRay.Color = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssFilmicTonemapSlope = function (self)
    if self.curPostProcess and self.curPostProcess.FilmicTonemap then
        return self.curPostProcess.FilmicTonemap.Slope
    end
    return nil
end

EnvSetData.SetPostProcesssFilmicTonemapSlope = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.FilmicTonemap then
            self.curPostProcess.FilmicTonemap = {}
        end
        
        self.curPostProcess.FilmicTonemap.Slope = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssFilmicTonemapToe = function (self)
    if self.curPostProcess and self.curPostProcess.FilmicTonemap then
        return self.curPostProcess.FilmicTonemap.Toe
    end
    return nil
end

EnvSetData.SetPostProcesssFilmicTonemapToe = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.FilmicTonemap then
            self.curPostProcess.FilmicTonemap = {}
        end
        
        self.curPostProcess.FilmicTonemap.Toe = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssFilmicTonemapShoulder = function (self)
    if self.curPostProcess and self.curPostProcess.FilmicTonemap then
        return self.curPostProcess.FilmicTonemap.Shoulder
    end
    return nil
end

EnvSetData.SetPostProcesssFilmicTonemapShoulder = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.FilmicTonemap then
            self.curPostProcess.FilmicTonemap = {}
        end
        
        self.curPostProcess.FilmicTonemap.Shoulder = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssFilmicTonemapBlackClip = function (self)
    if self.curPostProcess and self.curPostProcess.FilmicTonemap then
        return self.curPostProcess.FilmicTonemap.BlackClip
    end
    return nil
end

EnvSetData.SetPostProcesssFilmicTonemapBlackClip = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.FilmicTonemap then
            self.curPostProcess.FilmicTonemap = {}
        end
        
        self.curPostProcess.FilmicTonemap.BlackClip = value
        self.m_needsave = true
    end
end

EnvSetData.GetPostProcesssFilmicTonemapWhiteClip = function (self)
    if self.curPostProcess and self.curPostProcess.FilmicTonemap then
        return self.curPostProcess.FilmicTonemap.WhiteClip
    end
    return nil
end

EnvSetData.SetPostProcesssFilmicTonemapWhiteClip = function (self,value)
    if self.curPostProcess then
        if not self.curPostProcess.FilmicTonemap then
            self.curPostProcess.FilmicTonemap = {}
        end
        
        self.curPostProcess.FilmicTonemap.WhiteClip = value
        self.m_needsave = true
    end
end

EnvSetData.FullshData = function (self)
    -- 单机模式，或者房主，才能保存数据
    local isSingle = AccountManager:getMultiPlayer() == 0 or IsRoomOwner()
    if isSingle then
        if self.m_needsave then
            self.m_needsave = false
            local owid = G_GetWorldId()
            if CSOWorldMgr and owid then
                if not CSOWorldMgr:findWDescExt(owid) then
                    CSOWorldMgr:loadDescExt(owid)
                end
                local value = table2json(self.m_data)
                CSOWorldMgr:UpdateWDescExt(owid, self.datakey, value)
                CSOWorldMgr:saveWDescExtToFile(owid)
            end
        end
    else
        self.m_needsave = false
    end
end

EnvSetData.OnLoadData = function (self, owid)
    owid = owid or G_GetWorldId()
    --if self.m_owid ~= owid then
    self.m_owid = owid
    local isLoad = false
    if CSOWorldMgr and owid then
        if not CSOWorldMgr:findWDescExt(owid) then
            CSOWorldMgr:loadDescExt(owid)
        end
        local str =  CSOWorldMgr:GetWDescExtValue(owid, self.datakey)
        if str and str ~= "" then
            self.m_data = json2table(str)
            isLoad = true
        end
    end

    -- 没有加载任何数据，给一个初始的
    if not isLoad then
        self:InitData()
    end
    --end

    if self.m_data.SkyBox and self.m_data.SkyBox[self.m_data.curSkyTemplateIdx] then
        self.curSkyBox = self.m_data.SkyBox[self.m_data.curSkyTemplateIdx]
    end

    if  self.curSkyBox and  self.curSkyBox.TimeNode then
        self.curSkyTimeNode =  self.curSkyBox.TimeNode
    end

    if self.m_data.PostProcesss and self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx] then
        self.curPostProcess = self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx]
    end
end

EnvSetData.InitPostProcessInfo = function (self)
    self.m_needsave = true
    self.m_data.PostProcesss = {}

    self.m_data.PostProcesss[1] = copy_table(PostProcessTemplate[1])
    self.m_data.curPostProcessTemplateIdx = 1

    if self.m_data.PostProcesss and self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx] then
        self.curPostProcess = self.m_data.PostProcesss[self.m_data.curPostProcessTemplateIdx]
    end
end

EnvSetData.InitParticleInfo = function (self)
    self.m_needsave = true
    self.m_data.Particle = {}

    self.m_data.Particle = {
        Tex = "",
        Range = 1.0,
        Dir = {x=1.0,y=1.0,z=1.0},
        Speed = 1.0,
    }
end

EnvSetData.InitData = function (self)
    self.m_data.SkyBox = {}
    self.m_data.curSkyTemplateIdx = 1
    self.m_data.SkyBox[1] = copy_table(SkyTemplate[1])
    self:InitPostProcessInfo()
    self:InitParticleInfo()
end

EnvSetData.Clear = function (self)
    self.m_owid = nil
    self.m_data.TimePassing = 1.0
    self.m_needsave = false
end

EnvSetData.HasData = function (self)
    return self.m_data and self.m_data.SkyBox and #self.m_data.SkyBox > 0 and 
            self.m_data.PostProcesss and #self.m_data.PostProcesss > 0
end