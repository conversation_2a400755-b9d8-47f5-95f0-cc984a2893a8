
-- lua msg name define client2host
local tbGlobalMsg = SANDBOX_LUAMSG_NAME.GLOBAL
LuaMsgHandler = {}
function LuaMsgHandler:SubscribeLuaMsg()
	print("LuaMsgHandler:SubscribeLuaMsg")
	self:SubscribeClientHandle()
	self:SubscribeHostHandle()
	self:SubscribeTrackingPlayersHandle()
	self:SubscribeDisplayboardHandle();
end

-- 定义见 sandboxluanetmsgname.lua


-- 操作多人地图传送
function handleMultiMapTeleport(content, pcallback, useNewThread)
	print("handleMultiMapTeleport", content)
	local members = content.members;
	local callback = function (retCode, mapInfo)
		if pcallback then pcallback(retCode, mapInfo) end

		print("handleMultiMapTeleport call back", retCode, mapInfo)
		if retCode ~= 0 then return end

		-- 用于判断要不要给发起者发协议
		local selfUin = 0
		if GetGameInfo():GetRoomHostType() ~= ROOM_SERVER_RENT then
			if WorldMgr then
				selfUin = WorldMgr:getWorldOwnerUin()
			end
		end

		local telContent = {
			ret = retCode,
			mapinfo = mapInfo
		}
		for i, uin in ipairs(members) do
			if uin == selfUin then
				startMapTeleport(telContent)
			else
				-- 发送协议通知客户端传送
				SandboxLuaMsg.sendToClient(uin, tbGlobalMsg.MULTI_MAP_TELEPORT_TOCLIENT, telContent)
			end
		end
	end
	
	-- content包含字段 {mapid=xx, members={}, pos={}, body=xxx,country=xx}
	if useNewThread then
		threadpool:work(function ()
			GetInst("RoomService"):ReqJoinQuickupCSRoomByMultiTeleport(content, callback)
		end)
	else 
		GetInst("RoomService"):ReqJoinQuickupCSRoomByMultiTeleport(content, callback)
	end
end

-- 客户端发起请求多人地图传送
function ReqMultiMapTeleport(mapId, members, targetPos, callback, teleportmsg)
	print("ReqMultiMapTeleport", mapId, members, targetPos)
	local content = {
		mapid = mapId,
		members = members,
		pos = targetPos,
		teleportmsg = teleportmsg
	}
	content.body = GetInst("RoomService"):GetAppendQuickupPostData("")
	content.country = gFunc_getCountry()  -- 云服需要使用客机的country 否则全是英文
	if CurWorld and not CurWorld:isRemoteMode() then
		print("ReqMultiMapTeleport host or single")
		handleMultiMapTeleport(content, callback)
	else
		print("ReqMultiMapTeleport client or rent", content.body)
		SandboxLuaMsg.sendToHost(tbGlobalMsg.MULTI_MAP_TELEPORT_TOHOST, content)
	end
end

-- 同步studioparam给客机
function SyncStudioParam2Client(uin, paramstr)
	SandboxLuaMsg.sendToClient(uin, tbGlobalMsg.SYNC_STUDIO_PARAM_2_CLIENT, {params = paramstr})
end

-- 客户端发起进入多人地图匹配 operation  1-报名 2-取消报名 sceneId 50 活动，AppId 1009 增长
function ReqOperaMatchingTeleport(mapId, opera, type, sceneId,appId)
	local content = {
		type   = type,
		detail = {
			opera= opera,
			mapid= mapId,
			scene = sceneId,
			appid = appId,
		}
	}
	if CurWorld and CurWorld:isRemoteMode() then
		SandboxLuaMsg.sendToHost(_G.SANDBOX_LUAMSG_NAME.CLOUD.TELEPORT_REGISTER_TOHOST, content)
	end
end

-- 通知所有客机上报玩家参与悦享赛事地图小局游戏
function BPMathStartSingleGameNotify()
	print("BPMathStartSingleGameNotify")
    SandboxLuaMsg.sendBroadCast(_G.SANDBOX_LUAMSG_NAME.GLOBAL.BPMATH_START_SINGLE_GAME_NOTIFY_TOCLIENT, {})
end

-- 通知客机更新悦享赛事主推地图迷你枪战小局结算数据
function BPMathSingleResultNotify(uin, data)
	print("BPMathSingleResultNotify", uin, data)
    SandboxLuaMsg.sendToClient(uin, _G.SANDBOX_LUAMSG_NAME.GLOBAL.BPMATH_SINGLE_BATTLE_RESULT_NOTIFY_TOCLIENT, { 
        data = data
    })
end

-------------lua msg handle client fun ------------------------
LuaMsgHandler.luaOnMapTeleportClient = function(content)
	print("call LuaMsgHandler.luaOnMapTeleportClient on client")
	startMapTeleport(content)
end

-- 地图传送组队邀请
LuaMsgHandler.luaOnTeleportInvitedToHost = function(content)
	if not content then return end

	local selfUin = 0;
	if GetGameInfo():GetRoomHostType() ~= ROOM_SERVER_RENT then
		if WorldMgr then
			selfUin = WorldMgr:getWorldOwnerUin()
		end
	end
	if selfUin == content.beInvitedUin then
		LuaMsgHandler.luaOnTeleportInvitedToClient(content)
	else --帮忙转发
		local eventname = tbGlobalMsg.TELEPORT_SEND_INVITE_TOCLIENT;
		SandboxLuaMsg.sendToClient(content.beInvitedUin, eventname, content);
	end
end

LuaMsgHandler.luaOnTeleportInvitedToClient = function(content)
	if content then content.keep = true end --设置ESC不可关闭--
	if content then content.disableOperateUI = true end --设置游戏可操作--
	GetInst("MiniUIManager"):OpenUI("BeInvitePanel", "miniui/miniworld/cloudportal", "BeInvitePanelAutoGen", content)
end

--确认组队邀请 同意/拒绝
LuaMsgHandler.luaOnTpInviteConfirmToHost = function(content)
	if not content then return end

	local selfUin = 0;
	if GetGameInfo():GetRoomHostType() ~= ROOM_SERVER_RENT then
		if WorldMgr then
			selfUin = WorldMgr:getWorldOwnerUin()
		end
	end
	if selfUin == content.inviterUin then
		LuaMsgHandler.luaOnTpInviteConfirmToClient(content);
	else --帮忙转发
		local eventname = tbGlobalMsg.TELEPORT_INVITE_CONFIRM_TOCLIENT;
		SandboxLuaMsg.sendToClient(content.inviterUin, eventname, content);
	end
end

LuaMsgHandler.luaOnTpInviteConfirmToClient = function(content)
	local listCtrl = GetInst("MiniUIManager"):GetCtrl("MemberList");
	if listCtrl then --刷新下界面及数据--
		local status = content.status;
		local beInvited = content.beInvitedUin;
		listCtrl:SetMemberStatus(beInvited, status);	
	end
end

-- 通知上报大数据
LuaMsgHandler.luaOnHostAskClientReportCheat = function(content)
	if content and content.errCode then
		RoomCheatReport(content.errCode)
	end
end

-- 通知客户端上报大数据
function SendClientReprotCheat(uin, error_code)
	SandboxLuaMsg.sendToClient(uin, _G.SANDBOX_LUAMSG_NAME.CLOUD.REPORT_CHEAT_INFO_TOCLIENT, {errCode = error_code,})
end

-------------lua msg handle host fun ------------------------
-- 地图传送
LuaMsgHandler.luaMultiMapTeleportHost = function(content)
	print("call LuaMsgHandler.luaMultiMapTeleportHost on host")
	handleMultiMapTeleport(content, nil, true)
end

-- codeby fym 2022/01/21 心愿商人-反外挂逻辑处理：客机通知主机发奖
LuaMsgHandler.luaShopAdNpcHost_RewardHandle = function(info)
	print("call LuaMsgHandler.luaShopAdNpcHost_ExtralReward on host", info)
	-- 主机根据商品token值向web服务器发起商品详情校验，校验成功之后给客机发奖
	if not info or not info.player_uin or info.player_uin == 0 then return end
	if not info.order_id or info.order_id == 0 then return end
	local uin = info.player_uin
	local callback = function(ret)
		print("LuaMsgHandler.luaShopAdNpcHost_RewardHandle callback = ", ret)
		if ret and ret.ret == 0 and ret.data then
			local itemId = ret.data[1] and ret.data[1].id or 0
			local itemNum = ret.data[1] and ret.data[1].num or 0
			local player = nil
			if WorldMgr and WorldMgr.getPlayerByUin then
				player = WorldMgr:GetPlayerByUin(uin)
			elseif CurWorld and GetWorldActorMgr(CurWorld) then
				player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
			end
			if player and ClientCurGame and ClientCurGame:isInGame() then			
				player:gainItems(itemId, itemNum, 1, true);
			end
		end
	end
	local url = g_http_root.."miniw/business?"
	local reqParams = { act = 'take_cargo_order', id = info.order_id, user = uin }
	local paramStr,md5 = http_getParamMD5(reqParams)
	url = table.concat({url, paramStr, '&md5=', md5})
	print("luaShopAdNpcHost_RewardHandle", url)
	ns_http.func.rpc(url, callback, nil, nil, true, true);
end

-- codeby fym 2022/03/07 商城-仓库-游戏内道具提取-反外挂逻辑处理：客机通知主机提取道具
LuaMsgHandler.luaShopExtraStoreItem_RewardHandle = function(info)
	print("call LuaMsgHandler.luaShopExtraStoreItem_RewardHandle on host")
	if not info or not info.player_uin or info.player_uin == 0 then return end
	if not info.order_id or info.order_id == 0 then return end
	local uin = info.player_uin
	local callback = function(ret)
		print("LuaMsgHandler.luaShopExtraStoreItem_RewardHandle callback ret = ", ret)
		if ret and ret.ret == 0 and ret.data then
			local itemId = ret.data and ret.data.id or 0
			local itemNum = ret.data and ret.data.num or 0
			local player = nil
			if WorldMgr and WorldMgr.getPlayerByUin then
				player = WorldMgr:GetPlayerByUin(uin)
			elseif CurWorld and GetWorldActorMgr(CurWorld) then
				player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
			end
			if player and ClientCurGame and ClientCurGame:isInGame() then
				local addnum = player:gainItems(itemId, itemNum, 1, true);
				local code = (addnum and addnum >= 0) and ErrorCode.OK or ErrorCode.FAILED
				-- 主机通知客机提取道具状态
				print("SHOP_EXTRASTOREITEM_TOCLIENT uin =", uin, ", code = ", code, ", itemId = ", itemId, ", itemNum = ", itemNum)
				SandboxLuaMsg.sendToClient(uin, _G.SANDBOX_LUAMSG_NAME.GLOBAL.SHOP_EXTRASTOREITEM_TOCLIENT, { 
					code = code,
					itemId = itemId,
					itemNum = itemNum,
				})	
			end
		end
	end
	local url = g_http_root.."miniw/mall?"
	local reqParams = { act = 'verify_bag_item', token = info.order_id, role_id = uin }
	local paramStr, md5 = http_getParamMD5(reqParams)
	url = table.concat({url, paramStr, '&md5=', md5})
	ns_http.func.rpc(url, callback, nil, nil, true, true);
end

-- codeby fym 2022/04/15 商城-仓库-游戏内道具提取-反外挂逻辑处理：主机扣除道具成功之后通知客机提取道具状态
LuaMsgHandler.ShopExtraStoreItemState_ToClient = function(info)
	print("call LuaMsgHandler.ShopExtraStoreItemState_ToClient info = ", info)
	if not info or not info.code then return end
	threadpool:work(function ()
		if info.code == ErrorCode.OK then
			threadpool:wait(3);
			if GetInst("UIManager"):GetCtrl("ShopWareInfo") then
				GetInst("UIManager"):GetCtrl("ShopWareInfo"):ExtractItem_callback(info.code, info.itemId, info.itemNum)
			end
			if GetInst("MiniUIManager"):GetCtrl("StoreWareInfo") then
				GetInst("MiniUIManager"):GetCtrl("StoreWareInfo"):ExtractItem_callback(info.code, info.itemId, info.itemNum)
			end
		else
			ShowGameTipsWithoutFilter(GetS(t_ErrorCodeToString[info.code]), 3);
		end
	end)
end

function _report_action_log(uin, key, tab)
	if type(uin) ~= 'number' and type(uin) ~= 'string' then
		gFunc_SLOG("_report_action_log error type of uin is " .. type(uin))
		return
	end
	if type(key) ~= 'string' then
		gFunc_SLOG("_report_action_log error type of key is " .. type(key))
		return
	end

	if gFunc_ActionLogDetail then
		local ok, err = pcall(function()
			local s = JSON:encode(tab)
			gFunc_ActionLogDetail(uin, key, s)
		end)
		if not ok then
			gFunc_SLOG("_report_action_log error uin=" .. uin .. " key=" .. key .. ' err=' .. table.tostring(err))
			return
		end
	end
end
-- 开发者商店 客机通知主机提取道具
LuaMsgHandler.luaDeveloperstoreExtraStoreItemToHost = function(info)

	if isAbroadEvn() then
		-- 海外云服还是得通过主机发道具 现有逻辑不能校验订单 所以主机云服直接下发道具
		local uin = info.role_id
		if not info.itemid or info.itemid == 0 then return end
		if not info.itemnum or info.itemnum == 0 then return end
		local player = nil
		if WorldMgr and WorldMgr.getPlayerByUin then
			player = WorldMgr:GetPlayerByUin(uin)
		elseif CurWorld and GetWorldActorMgr(CurWorld) then
			player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
		end
		if player and ClientCurGame and ClientCurGame:isInGame() then
			local realitemid = info.realitemid or info.itemid
			player:gainItems(realitemid, info.itemnum, 1, true);
			OnDeveloperBuyItemTriggerEventHost(info.itemid, uin,info.itemnum)
		end
		return
	end

	print("hx luaDeveloperstoreExtraStoreItemToHost info",info)
	gFunc_SLOG("developer_store extra start info=" .. table.tostring(info))
	if not info or not info.role_id or info.role_id == 0 then return end
	if not info.order_id or info.order_id == 0 then return end
	if not info.mapid or info.mapid == 0 then return end
	if not info.itemid or info.itemid == 0 then return end
	if not info.itemnum or info.itemnum == 0 then return end
	if not info.authoruin or info.authoruin == 0 then return end
	if not info.realitemid then return end
	if not info.version then return end
	if not info.seq then return end
	local developerStoreFromowid = WorldMgr:getFromWorldID()
	local isFromModPackWorld = AccountManager:IsCurWorldFromModPack()
	if isFromModPackWorld and WorldMgr.getWoldPwid then
		developerStoreFromowid = WorldMgr:getWoldPwid()
	end
	local itemdef = AccountManager:DeveloperStoreGetItemDef(info.itemid, info.resid)
	if not itemdef then
		return
	elseif itemdef.ID ~= info.realitemid then
		return
	end
	if not WorldMgr or tonumber(info.mapid) ~= developerStoreFromowid then
		-- 校验地图ID与当前地图是否匹配
		gFunc_SLOG("luaDeveloperstoreExtraStoreItemToHost mapid not match info.mapid=" .. info.mapid .. ",room map id= " .. developerStoreFromowid)
		print("mapid not match info.mapid=" .. info.mapid .. "WorldMgr.FromWorldID=" .. developerStoreFromowid)
		return 
	end
	print("hx luaDeveloperstoreExtraStoreItemToHost",1)

	--threadpool:work(function ()
		local role_id = info.role_id;
		local order_id = info.order_id;
		local mapid = info.mapid;
		local itemid = info.itemid;
		local itemnum = info.itemnum;
		local authoruin = info.authoruin;
		local version = info.version;
		local realitemid = info.realitemid
		local seq = info.seq;
		print("hx luaDeveloperstoreExtraStoreItemToHost",2)
		local uin = info.role_id
		local url = g_http_root.."miniw/mall?"
		local reqParams = { act = 'verify_bag_item', token = order_id, role_id = role_id, HostUin = 0,
							AuthorUin = authoruin, MapID = mapid, ItemID = itemid, ItemNum = itemnum,
							MapBagVersion = version }
		local paramStr, md5 = http_getParamMD5(reqParams)
		url = table.concat({url, paramStr, '&md5=', md5})
		print("hx luaDeveloperstoreExtraStoreItemToHost",3)
		local callback = function(ret)
			print("hx luaDeveloperstoreExtraStoreItemToHost ret",ret)

			gFunc_SLOG("developer_store callback uin=" .. uin .. " ret=" .. table.tostring(ret) .. " info=" .. table.tostring(info))
			if not ret then
				-- 失败
				SandboxLuaMsg.sendToClient(role_id, _G.SANDBOX_LUAMSG_NAME.GLOBAL.DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT, {code = -1 })
				_report_action_log(uin, "developer_store_order_failed", {reason="no ret", info=info})
				return
			end
			if ret.ret == ErrorCode.DEV_MAPSTORE_NEED_REFRESH or ret.ret == ErrorCode.DEV_MAPBAG_NEED_REFRESH then
				if seq < 3 then 
					SandboxLuaMsg.sendToClient(role_id,_G.SANDBOX_LUAMSG_NAME.GLOBAL.DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT, { 
						code = 0,
						itemid = itemid,
						itemnum = itemnum,
						reshtypepe = ret.ret,
						role_id = role_id,
						order_id = order_id,
						mapid = mapid,
						authoruin = authoruin,
						realitemid = realitemid,
						seq = seq
					})	
				end
				_report_action_log(uin, "developer_store_order_failed", {reason="need refresh", info=info})
			elseif ret.ret == 0 and ret.data then
				local itemId = ret.data and ret.data.id or 0
				local itemNum = ret.data and ret.data.num or 0
				local player = nil
				if WorldMgr and WorldMgr.getPlayerByUin then
					player = WorldMgr:GetPlayerByUin(uin)
				elseif CurWorld and GetWorldActorMgr(CurWorld) then
					player = GetWorldActorMgr(CurWorld):findPlayerByUin(uin)
				end

				local ok, err = pcall(function()
					local s = JSON:encode({extra_1 = order_id, extra_2 = "" .. itemid, extra_3 = "" .. itemnum})
					gFunc_ActionLogDetail(uin, "developer_store_gain_item", s)
					
					if gFunc_ActionLoggerClientReport then
						gFunc_ActionLoggerClientReport(uin, 64003, "developer_store_gain_item", s)
					end	
				end)
				if not ok then
					print("gFunc_ActionLogDetail DeveloperStoreItem error: " .. table.tostring(err))
					gFunc_SLOG("gFunc_ActionLogDetail DeveloperStoreItem error: " .. table.tostring(err))
				end
				if player and ClientCurGame and ClientCurGame:isInGame() then
					SandboxLuaMsg.sendToClient(role_id,_G.SANDBOX_LUAMSG_NAME.GLOBAL.DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT, { 
						code = 0,
						itemid = itemid,
						realitemid = realitemid,
						itemnum = itemnum,
					})					
					player:gainItems(realitemid, itemNum, 1, true);
					OnDeveloperBuyItemTriggerEventHost(realitemid, uin,itemnum,info.resid)
				end
			elseif ret.ret then
							SandboxLuaMsg.sendToClient(role_id,_G.SANDBOX_LUAMSG_NAME.GLOBAL.DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT, { 
					code = ret.ret
				})
				
				_report_action_log(uin, "developer_store_order_failed", {reason="ret code " .. ret.ret, info=info})
			else
				print("hx LuaMsgHandler.luaDeveloperstoreExtraStoreItem_RewardHandle2 callback ret = ", ret)
				SandboxLuaMsg.sendToClient(role_id, _G.SANDBOX_LUAMSG_NAME.GLOBAL.DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT, {code = ret })
				_report_action_log(uin, "developer_store_order_failed", {reason="default", info=info})
			end
			
		end
		
		ns_http.func.rpc(url, callback, nil, nil, true, true);
		print("hx luaDeveloperstoreExtraStoreItemToHost url",url)
	--end)
end

-- 开发者商店 主机通知客机提取道具状态
LuaMsgHandler.luaDeveloperstoreExtraStoreItemToClient = function(info)
	if not info or not info.code then 
		SetItemCanBuyAgain(true)
		return 
	end

	threadpool:work(function ()
		if info.code == ErrorCode.OK then
			if info.reshtypepe then
				DeveloperStoreResh(info.seq,info.reshtypepe,info.role_id,info.authoruin,info.mapid,
									info.itemid,info.itemnum,info.order_id, info.realitemid, info.resid)
				return
			else
				DeveloperExtraStoreSuccess(info.realitemid or info.itemid,info.num)
			end
		else
			ShowGameTipsWithoutFilter(GetS(t_ErrorCodeToString[info.code]), 3);
		end

		SetItemCanBuyAgain(true)
	end)
end

LuaMsgHandler.luaActAwardGainItemToHost = function(info)
	if PlatformUtility:isPureServer() then
		--暂时不支持云服，云服没法setKv
		return
	end
	AccountManager:ActAwardGainItemHostListener(info)
end

LuaMsgHandler.luaActAwardGainItemToClient = function(info)
	AccountManager:ActAwardGainItemClientListener(info)
end

-- 订单校验
LuaMsgHandler.luaOnCoinConvertStar = function(content)
	MiniLog("call luaOnCoinConvertStar ", FormatPrint(2, content,"content"))
	local verifyStarCallback = function(ret)
		MiniLog("call verifyStarCallback", FormatPrint(2, ret,"content"))
		if ret and ret.ret == 0 and ret.data then
			local itemNum = ret.data.num
			if WorldMgr and WorldMgr.getPlayerByUin then
				local player = WorldMgr:GetPlayerByUin(content.player_uin)
				if player then
					player:starConvert(itemNum * MiniCoin_Star_Ratio)
					SandboxLuaMsg.sendToClient(content.player_uin, _G.SANDBOX_LUAMSG_NAME.GLOBAL.COIN_CONVERT_STAR_TOCLIENT, { success = true})
					return
				end
			else
				MiniLog("[ERROR]verifyStarCallback failed! worldmgr is nil!!")
			end
		else
			gFunc_ActionLog(content.player_uin, "coin_converter_star_failed", FormatPrint(2, content,"content"))
		end
		MiniLog("[ERROR]luaOnCoinConvertStar failed! ", ret and FormatPrint(2, ret,"ret") or "")
		SandboxLuaMsg.sendToClient(content.player_uin, _G.SANDBOX_LUAMSG_NAME.GLOBAL.COIN_CONVERT_STAR_TOCLIENT, { success = false})
	end

	if not content or not content.order_id or not content.player_uin then
		verifyStarCallback(nil)
	end

	local url = g_http_root.."miniw/mall?"
	local reqParams = { act = 'verify_convert_star', token = content.order_id, role_id = content.player_uin }
	local paramStr, md5 = http_getParamMD5(reqParams)
	url = table.concat({url, paramStr, '&md5=', md5})
	MiniLog("luaOnCoinConvertStar call url", url)
	ns_http.func.rpc(url, verifyStarCallback, nil, nil, true, true);
end

-- 星星惩罚
LuaMsgHandler.luaOnStarPunishToHost = function(content)
	local player
	content.uin = tonumber(content.uin)
	MiniLog("luaOnStarPunishToHost content.uin="..content.uin)
	if content.uin then
		if WorldMgr and WorldMgr.getPlayerByUin then
			player = WorldMgr:GetPlayerByUin(content.uin)
			MiniLog("luaOnStarPunishToHost1")
		elseif CurWorld then
			player = GetWorldActorMgr(CurWorld):findPlayerByUin(content.uin)
			MiniLog("luaOnStarPunishToHost2")
		end
	end
	if player then
		local attrib = player:getAttrib();
		attrib = tolua.cast(attrib, "PlayerAttrib");
		MiniLog("luaOnStarPunishToHost3")
		if attrib then
			local Remove_Need_Star_New = 1
			local StarDebuffTime=attrib:getStarDebuffTime()
			local tick2sec=20
			local unitSec=LuaConstants:get().revive_in_place_consume_buff_clear_unit*tick2sec
			local starNum = math.floor(attrib:getExp()/EXP_STAR_RATIO);
			Remove_Need_Star_New=math.ceil((LuaConstants:get().revive_in_place_consume_buff_clear_consume*(math.ceil(StarDebuffTime)))/(unitSec))--计算所需星星
			local starNum = math.floor(attrib:getExp()/EXP_STAR_RATIO);
			if starNum >= Remove_Need_Star_New then
				attrib:addExp(-Remove_Need_Star_New * EXP_STAR_RATIO);--扣除客机星星
				attrib:setStarDebuffTime(0)
				attrib:setStarDebuffStage(0)
				MiniLog("luaOnStarPunishToHost addExp")
			end
			MiniLog("luaOnStarPunishToHost4")
		end
	end
end

LuaMsgHandler.luaOnCoinConvertStarToClient = function(content)
	MiniLog("call luaOnCoinConvertStarToClient", FormatPrint(2, content,"ret"))
	AccountManager:onConvertStartRet(content.success)
end


LuaMsgHandler.luaOnMultNorRoomInfoChangedToClient = function(content)
	print("call LuaMsgHandler.luaOnMultNorRoomInfoChangedToClient on client")
	if PlatformUtility:isPureServer() then
		return
	end
	if handleMultNorRoomInfoChangedNotify then
		handleMultNorRoomInfoChangedNotify(content)
	end
end

LuaMsgHandler.luaOnMultCloudRoomInfoChangedToClient = function(content)
	print("call LuaMsgHandler.luaOnMultCloudRoomInfoChangedToClient on client")
	if PlatformUtility:isPureServer() then
		return
	end
	if handleMultCloudRoomInfoChangedNotify then
		handleMultCloudRoomInfoChangedNotify(content)
	end
end


LuaMsgHandler.luaOnCloudKickOffPlayerToClient = function(content)
	print("call LuaMsgHandler.luaOnCloudKickOffPlayerToClient on client")
	if GetInst("CloudVipKickoffDataMgr") then
		GetInst("CloudVipKickoffDataMgr"):OnClientReceiveVoteRequest(content)
	end
end

LuaMsgHandler.luaOnCloudOtherPlayerVoteKickOffPlayerToClient = function(content)
	-- body
	if GetInst("CloudVipKickoffDataMgr") then
		GetInst("CloudVipKickoffDataMgr"):OnClientReceiveOtherVoteRequest(content)
	end
end

LuaMsgHandler.luaOnCloudKickOffPlayerEndToClient  = function (content)
	-- body
	if GetInst("CloudVipKickoffDataMgr") then
		GetInst("CloudVipKickoffDataMgr"):OnClientReceiveVoteEnd(content)
	end
end



LuaMsgHandler.luaOnMultCloudRoomInfoChangedToHost = function(content)
	print("call LuaMsgHandler.luaOnMultCloudRoomInfoChangedToHost on client")
	if PlatformUtility:isPureServer() then
		if handleRentCloudRoomInfoChangedNotify then
			handleRentCloudRoomInfoChangedNotify(content)
		end
	end
end

LuaMsgHandler.luaOnMultCloudNotifyMemberTeamDescToHost = function(content)
	print("call LuaMsgHandler.luaOnMultCloudNotifyMemberTeamDescToHost on client")
	if PlatformUtility:isPureServer() then
		if handleRentCloudMemberTeamDescNotify then
			handleRentCloudMemberTeamDescNotify(content)
		end
	end
end

LuaMsgHandler.luaOnMultCloudNotifyMemberTeamDescToClient = function(content)
	print("call LuaMsgHandler.luaOnMultCloudNotifyMemberTeamDescToClient on client")
	if not PlatformUtility:isPureServer() then
		if handleClientCloudMemberTeamDescNotify then
			handleClientCloudMemberTeamDescNotify(content)
		end
	end
end

-- 游商打开商店
LuaMsgHandler.luaOnRegisterNpcTraderOpenUI = function (content)
	GetInst("NpcTradeStoreMgr"):OpenNpcTraderStore(content);
end

-- 游商在商店交易购买回复
LuaMsgHandler.luaOnRegisterNpcTradeBuyRespone = function (content)
	if GetInst("NpcTradeStoreMgr"):IsShown() then
		GetInst("NpcTradeStoreMgr"):npcTradeRespone(content)
	end
end

LuaMsgHandler.luaOnRegisterToolboxHistory =  function (content)
	AddItemHistoryToClient(content.itemID,content.objID)
end

-- 技能编辑ShowUITimeline同步客机操作
LuaMsgHandler.luaOnShowScreenUIToClient =  function (content)
	local isShow = content.isshow
	if isShow == false then 
		InitScreenEffect() --隐藏
	else
		ShowScreenEffect(content.type,content.isloop,content.incspeed)	
	end
	
end

-- 主客机同步 触发器/脚本 日志协议
LuaMsgHandler.luaOnCloudTriggerDebugInfoToClient = function(content)
	print("call LuaMsgHandler.luaOnCloudTriggerDebugInfoToClient on client ".. content.key)
	-- _G.Trigger.Debug:ReceiveDataFromHost(content.key, content.data)
	if content.key == "trigger_config_info" then  -- 收到触发器组/触发器配置信息
		if ScriptSupportDebug then
			ScriptSupportDebug:clearTriggerConfig()
			ScriptSupportDebug:clearTriggerInfo()

			for _, groups in pairs(content.config_list) do
				-- table 中的数字key 传输后变成了字符串, 需要转换一次
				-- 这是传输协议使用json转换导致的问题, 目前只能自行转换
				local t = {}
				for _key, _val in pairs(groups) do
					if string.match(_key, "[0-9]+") then
						if type(_val) == "table" then
							local in_val = {}
							for __key, __val in pairs(_val) do
								if string.match(__key, "[0-9]+") then
									in_val[tonumber(__key)] = __val
								else
									in_val[__key] = __val
								end
							end
							t[tonumber(_key)] = in_val
						else
							t[tonumber(_key)] = _val
						end

					else
						t[_key] = _val
					end
				end
				ScriptSupportDebug:setTriggerConfig(groups.sstype, t)
			end
			for _, data in pairs(content.info_list) do
				ScriptSupportDebug:setTriggerInfo(data)
			end
		end
	elseif content.key == "trigger_info" then  -- 收到同步多条触发器info
		if ScriptSupportDebug then
			if content.first_page then
				ScriptSupportDebug:clearTriggerInfo()
			end
			for _, _info in pairs(content.data) do
				ScriptSupportDebug:setTriggerInfo(_info)
			end
		end
	elseif content.key == "trigger_config" then  -- 收到同步多条触发器 config
		if ScriptSupportDebug then
			if content.first_page then
				ScriptSupportDebug:clearTriggerConfig()
			end

			for _, groups in pairs(content.data) do
				-- table 中的数字key 传输后变成了字符串, 需要转换一次
				-- 这是传输协议使用json转换导致的问题, 目前只能自行转换
				local t = {}
				for _key, _val in pairs(groups) do
					if string.match(_key, "[0-9]+") then
						if type(_val) == "table" then
							local in_val = {}
							for __key, __val in pairs(_val) do
								if string.match(__key, "[0-9]+") then
									in_val[tonumber(__key)] = __val
								else
									in_val[__key] = __val
								end
							end
							t[tonumber(_key)] = in_val
						else
							t[tonumber(_key)] = _val
						end

					else
						t[_key] = _val
					end
				end
				ScriptSupportDebug:setTriggerConfig(groups.sstype, t)
			end
		end
	elseif content.key == "trigger_log" then  -- 收到同步多条触发器日志信息
		if ScriptSupportDebug then
			local tab = content.data or {}
			for _, tab in pairs(content.data or {}) do
				ScriptSupportDebug:addLog(tab.logidx or 0, tab or {})
			end
		end
	elseif content.key == "init_trigger_log" then  -- 收到同步全量的触发器日志信息
		-- pass
	elseif content.key == "init_script_log" then -- 收到同步全量开发者日志信息
		if scriptprintDataMgr then
			scriptprintDataMgr:ClearLog()
		end
	elseif content.key == "script_log" then -- 收到同步多条开发者日志信息
		if scriptprintDataMgr then
			scriptprintDataMgr:AddLogNodes( content.data )
		end
	elseif content.key == "open" then  -- 收到通知客户端开启调试开发(打开后才能正常显示)
		if ScriptSupportDebug then
			ScriptSupportDebug:setDebugOnOff(true)
			ScriptSupportDebug:setCloudClientDebug(true)
			ScriptSupportDebug:startLog()
		end
	elseif content.key == "block_info" then
		if ScriptSupportDebug then
			if content.first_page then
				ScriptSupportDebug:clearBlockInfo()
			end
			for _, _info in pairs(content.data) do
				local t = {}
				for _key, _val in pairs(_info) do
					if string.match(_key, "[0-9]+") then
						if type(_val) == "table" then
							local in_val = {}
							for __key, __val in pairs(_val) do
								if string.match(__key, "[0-9]+") then
									in_val[tonumber(__key)] = __val
								else
									in_val[__key] = __val
								end
							end
							t[tonumber(_key)] = in_val
						else
							t[tonumber(_key)] = _val
						end

					else
						t[_key] = _val
					end
				end
				ScriptSupportDebug:addBlockInfo(t)
			end
		end
	elseif content.key == "init_block_log" then
		ScriptSupportDebug:clearBlockLog()
	elseif content.key == "block_log" then
		if ScriptSupportDebug then
			local tab = content.data or {}
			for _, v in pairs(tab) do
				ScriptSupportDebug:addBlockLog(tab.logidx or 0, v)
			end
		end
		if ScriptSupportDebug then
			if content.first_page then
				ScriptSupportDebug:clearBlockInfo()
			end
			for _, _info in pairs(content.data) do
				local t = {}
				for _key, _val in pairs(_info) do
					if string.match(_key, "[0-9]+") then
						if type(_val) == "table" then
							local in_val = {}
							for __key, __val in pairs(_val) do
								if string.match(__key, "[0-9]+") then
									in_val[tonumber(__key)] = __val
								else
									in_val[__key] = __val
								end
							end
							t[tonumber(_key)] = in_val
						else
							t[tonumber(_key)] = _val
						end
					else
						t[_key] = _val
					end
				end
				ScriptSupportDebug:addBlockInfo(t)
			end
		end
	end
end
--主机获取进入房间玩家的设备信息
LuaMsgHandler.luaOnTriggerStandReportEventInfoToClient = function(content)
	if GameVmReport then
		_G.GameVmReport:Handle2Client(content)
	end
end

-- 主机收到客机请求开启同步触发器日志到客机
-- 目前代码逻辑中云服默认只给地图作者开启了调试日志
-- 提供一个接口给客机可以请求调试
LuaMsgHandler.luaOnCloudRequestStartTriggerDebugInfoToHost = function(content)
	print("call LuaMsgHandler.luaOnCloudRequestStartTriggerDebugInfoToHost on client")
	if content and content.uin then
		SyncServerLog:InitNewTracker(content.uin)
	end
end
--------- 大会员踢人-----------------------------------
LuaMsgHandler.luaOnCloudRequestStartKickOffPlayerToHost = function(content)
	print("call LuaMsgHandler.luaOnCloudRequestStartKickOffPlayerToHost ", content)
	-- body

	-- 使用 
	if not content then
		return
	end
	if content.sender and content.sender ~= content.player_uin then
		-- 使用 content.sender 判断投票是否伪造  2024.08.07 by huanglin
		if gFunc_ActionLogDetail then
			local log = {
				sender = content.sender,
				dest = content.uin or 0,
				type = "start",
			}
			gFunc_ActionLogDetail(content.player_uin or 0, "cheat_vote_kickoff", JSON:encode(log))
		end
		return
	end
	if GetInst("CloudVipKickoffDataMgr") then
		GetInst("CloudVipKickoffDataMgr"):OnReceiveStartVoteRequest(content)
	end
end

LuaMsgHandler.luaOnCloudRequestVoteKickOffPlayerToHost = function(content)
	print("call LuaMsgHandler.luaOnCloudRequestVoteKickOffPlayerToHost ", content)
	-- body

	if not content then
		return
	end
	if content.sender and content.sender ~= content.player_uin then
		-- 使用 content.sender 判断投票是否伪造 2024.08.07 by huanglin
		if gFunc_ActionLogDetail then
			local log = {
				sender = content.sender,
				dest = content.uin or 0,
				type = "vote",
			}
			gFunc_ActionLogDetail(content.player_uin or 0, "cheat_vote_kickoff", JSON:encode(log))
		end
		return
	end
	if GetInst("CloudVipKickoffDataMgr") then
		GetInst("CloudVipKickoffDataMgr"):OnReceiveVoteRequest(content)
	end
end
--------- 大会员踢人-----------------------------------


LuaMsgHandler.luaOnActorAttrSetChange = function(content)
	print("call LuaMsgHandler.luaOnActorAttrSetChange on client")
	if not content or not content.objid or not content.attrtype or not content.val then
		return
	end
	if not CurWorld then
		return 
	end
	local actor = GetWorldActorMgr(CurWorld):findActorByWID(content.objid)
	if not actor then 
		return
	end
	local att = actor:getAttrib()
	if not att then
		return
	end
	if content.attrtype == ATTRT_MAX_HP then
		att:setMaxHP(content.val)
	elseif content.attrtype == ATTRT_VACANT_ENERGY then
		ActorComponentCallModule(actor,"VacantComponent","setCurVacantEnergy", content.val)
	end
end


LuaMsgHandler.luaOnMobChangeSaddleToTrackingPlayers = function(content)
	print("call LuaMsgHandler.luaOnMobChangeSaddle on TrackingPlayers")
	if not content or not content.objid or not content.SaddleID then
		return
	end
	if not CurWorld then
		return
	end
	local mob = GetWorldActorMgr(CurWorld):findMobByWID(content.objid)
	if not mob or not mob:getBody() then 
		return
	end
	mob:getBody():showSaddle(content.SaddleID)

end

LuaMsgHandler.luaOnMobChangeScorpionNecklace = function(content)	
	if not content or not content.objid then
		return
	end
	if CurWorld then
		local mob = GetWorldActorMgr(CurWorld):findMobByWID(content.objid)
		if not mob or not mob:getBody() then 
			return
		end
		mob:getBody():showNecklace(content.NecklaceID)
	end
end

LuaMsgHandler.luaOnMobChangeScorpionHideStatus = function(content)	
	if not content or not content.objid then
		return
	end
	if not CurWorld then
		return 
	end
	local actor = GetWorldActorMgr(CurWorld):findMobByWID(content.objid)
	if not actor then 
		return
	end
	local actorBody = actor:getBody() 
	if not actorBody then 
		return
	end

	if content.playAnim == 0 then
		if actorBody:getIsShow() then
			return
		end					  		
		threadpool:work(function()				
			local time = 0
			while (actorBody.getModel and not actorBody:getModel()) do				
				time = time + 2	
				if time >= 10 then
					return
				end						
				threadpool:wait(2)
				if not CurWorld then
					return
				end				
				actor = GetWorldActorMgr(CurWorld):findMobByWID(content.objid)
				if not actor then 
					return
				end
				actorBody = actor:getBody() 
				if not actorBody then 
					return
				end
			end	
			
			actorBody:playAnim(SEQ_SCORPION_DIRLLOUT) 
			actorBody:show(true, true)	
			threadpool:delay(0.3,function ()
				actorBody:stopAnim(SEQ_SCORPION_DIRLLOUT) 
			end)
		end)
	elseif content.playAnim == 1 then
		if not actorBody:getIsShow() then
			return
		end						
		threadpool:work(function()				
			local time = 0
			while (actorBody.getModel and not actorBody:getModel()) do	
				time = time + 2	
				if time >= 10 then
					return
				end						
				threadpool:wait(2)
				if not CurWorld then
					return
				end
				actor = GetWorldActorMgr(CurWorld):findMobByWID(content.objid)
				if not actor then 
					return
				end
				actorBody = actor:getBody() 
				if not actorBody then 
					return
				end
			end	
			
			actorBody:playAnim(SEQ_SCORPION_HIDE) 
			threadpool:delay(0.5,function ()		
				actorBody:stopAnim(SEQ_SCORPION_HIDE)
				actorBody:show(false)  
				actorBody:setHPVisible(false)   
			end)
		end)
	end
end

-- 坐骑技能播放特效同步主机
LuaMsgHandler.luaOnHorsePlayEffectToHost = function(content)
	if not CurWorld then return end

	if not content or not content.objid then
		return
	end

	local actorHorse = GetWorldActorMgr(CurWorld):findActorHorsebByWID(content.objid)
	if actorHorse then --坐骑特效播放--
		if content.isplay and content.isplay == 1 then
			ActorComponentCallModule(actorHorse,"EffectComponent","playBodyEffect",content.effect or "")
		else
			ActorComponentCallModule(actorHorse,"EffectComponent","stopBodyEffect",content.effect or "")
		end
	end
end

LuaMsgHandler.luaOnHorseSkillErrorCode = function (content)
	if not CurWorld then return end
	if content.strid then
	elseif content.countdown and tonumber(content.countdown) then
		ShowGameTips(content.countdown)
	end
end

LuaMsgHandler.desertBuinessmanDealHandle = function(content)
	if content.special and content.uin > 0 and content.sucess ~= nil then
		--userTaskReportedGlobal(-1, UserTaskReportType_DEAL, 3212, -1, -1);
		if not CurWorld then
			return;
		end
		local worldDesc = AccountManager:findWorldDesc(WorldMgr:getWorldId());
		local serMapInfo = nil
		if not worldDesc then
			serMapInfo = GetMapInfoFromCache and GetMapInfoFromCache(WorldMgr:getWorldId()) or mapservice.mapInfoCache[WorldMgr:getWorldId()];
		end
		local trueId;
		local worldtype;
		local realOwner;
		if worldDesc then
			if worldDesc.fromowid and worldDesc.fromowid ~= 0 then
				trueId = worldDesc.fromowid
			else
				trueId = worldDesc.worldid
			end
			worldtype = worldDesc.worldtype;
			realOwner = worldDesc.realowneruin;
		elseif serMapInfo then
			worldtype = serMapInfo.worldtype;
			trueId = serMapInfo.owid;
			realOwner = serMapInfo.author_uin;
		else
			worldtype = WorldMgr:getGameMode();
			trueId = WorldMgr:getFromWorldID();
			realOwner = WorldMgr:getRealOwnerUin();		
		end

	--	if worldDesc then
		
			local playType = 0;
			local since_create = AccountManager:get_time_since_create() or 0;
			if since_create < 86400 then
				 playType = 1;
			end
			local multiPlayer = AccountManager:getMultiPlayer() > 0 and "multi" or "single";
			if ROOM_SERVER_RENT == GetGameInfo():GetRoomHostType() then
				multiPlayer =  "multi";
			end
			local isOwner = realOwner == AccountManager:getUin() and 1 or 0;
			local stringType;
			if worldtype == 0 then
				stringType = "_adventure";
			elseif worldtype == 2 then
				stringType = "_extreme-adventure";
			elseif worldtype == 6 then
				stringType = "_advanced-adventure";
			end
			if stringType then

			end
	--	end
	end
	if not content.special then
		if CurWorld and not CurWorld:isRemoteMode() then
			local objid = content.objId;
			local itemid = content.itemId;
			if itemid == nil or itemid < 0 then
				return;
			end
			local mob = GetWorldActorMgr(CurWorld):findMobByWID(objid);
			mob = tolua.cast(mob, "ActorDesertBusInessMan");
			if mob then
				mob:setShouldPlayDealAnim(itemid);
			end
		end
	end
end

LuaMsgHandler.userTaskReport = function(content)
	if content and content.type and content.id1 and content.id2 and content.id3 then
		userTaskReportedGlobal(-1, content.type, content.id1, content.id2, content.id3)
	end
end

LuaMsgHandler.luaOnRecvStudioParam = function(content)
	-- 客机收到参数处理
	local params = content.params
	MiniLog("luaOnRecvStudioParam ", params)
end

LuaMsgHandler.syncNpcAiTalkSendMessageResponeFunc = function(content)
	print("syncNpcAiTalkSendMessageResponeFunc",table2json(content))
	SandboxLuaMsg.npcAiTalkSendMsgRespone(content)
end

LuaMsgHandler.syncNpcAiTalkGetMessageResponeFunc = function(content)
	print("syncNpcAiTalkGetMessageResponeFunc",table2json(content))
	SandboxLuaMsg.npcAiTalkGetMsgRespone(content)
end

LuaMsgHandler.syncCloudAnnoucementFunc = function(content)
	print("syncCloudAnnoucementFunc",table2json(content))
	SandboxLuaMsg.cloudAnnouncement(content)
end

LuaMsgHandler.luaGetDriftBottleHandleHost = function(content)
	if not content.spawnType then
		return;
	end
	if content.spawnType == 1 then
		if not content or not content.uin then
			return;
		end
		local player;
		if WorldMgr and WorldMgr.getPlayerByUin then
			player = WorldMgr:GetPlayerByUin(content.uin)
		elseif CurWorld and GetWorldActorMgr(CurWorld) then
			player = GetWorldActorMgr(CurWorld):findPlayerByUin(content.uin)
		end
		if player then
			player:gainItemsUserdata(11806, 1, content.str, 1);
		end
	else
		if not content or not content.str or not content.x or not content.y or not content.z then
			return;
		end
		local grid = BackPackGrid:new_local();
		grid:setItem(11806, 1);
		grid:setUserdataStr(content.str);
		local wcoord = WCoord:new_local();
		wcoord.x = content.x;
		wcoord.y = content.y;
		wcoord.z = content.z;
		GetWorldActorMgr(CurWorld):spawnItem(wcoord, grid);
	end
end

LuaMsgHandler.luaOnRegisterTeleportToHost = function (content)
	print("luaOnRegisterTeleportToHost data=" .. (content and table.tostring(content) or "nil"))
	if not content or not content.player_uin or not content.type or not content.detail or not content.detail.opera then
		print("luaOnRegisterTeleportToHost param err")
		return
	end
	if not TeleportRegisterManager then
		print("luaOnRegisterTeleportToHost mgr not exists")
		return
	end

	if content.detail.opera == 1 then
		-- 报名
		if not content.detail.mapid then
			print("luaOnRegisterTeleportToHost mapid not set")
			return
		end
		if not TeleportRegisterManager.playerRegister then
			print("luaOnRegisterTeleportToHost playerRegister not support")
			return
		end
		TeleportRegisterManager:playerRegister(content.player_uin, content.detail.mapid, content.detail.scene or 0, content.detail.appid or 0)
	elseif content.detail.opera == 2 then
		if not TeleportRegisterManager.playerCancel then
			print("luaOnRegisterTeleportToHost playerCancel not support")
			return
		end
		TeleportRegisterManager:playerCancel(content.player_uin)
	else
		print("luaOnRegisterTeleportToHost unknown opera " .. content.detail.opera)
		return
	end
end

LuaMsgHandler.luaOnRegisterNpcTraderBuy = function (content)
	local ret = GetInst("NpcTradeStoreMgr"):GoodsTrade(content)
	if ret ~= 0 then
		-- ShowGameTips(GetS(9286), 1);
	end
	local param = {code = ret, goodsData= GetInst("NpcTradeStoreMgr"):GetNpcTradeData(), reqParam = content}	
	SandboxLuaMsg.sendToClient(content.objID, SANDBOX_LUAMSG_NAME.Survive.NPC_TRADE_BUY_RESPONE, param);
end

LuaMsgHandler.luaReplaceGridWithUserdataStr= function(content)
	if not content or not content.playerid or not content.itemid or not content.itemnum or not content.userdata or not content.index then
		return
	end
	local player = nil
	if WorldMgr and WorldMgr.getPlayerByUin then
		player = WorldMgr:GetPlayerByUin(content.playerid)
	elseif CurWorld and GetWorldActorMgr(CurWorld) then
		player = GetWorldActorMgr(CurWorld):findPlayerByUin(content.playerid)
	end
	if player then
		-- 仅支持染色块修改
		local toolId = player:getCurToolID()
		if not IsDyeableBlockLua(toolId) or not IsDyeableBlockLua(content.itemid) then
			gFunc_ActionLog(content.playerid, "cheat_replace_item",
			 "luaReplaceGridWithUserdataStr toolid:" .. tostring(toolId) .. 
			 " target itemid:" .. tostring(content.itemid) .. " num:" .. tostring(content.itemnum))
			return
		end
		local bp = player:getBackPack();
		if bp then
			-- bp:replaceItem(content.index, content.itemid,content.itemnum, -1,0,0,nil,content.userdata)
			local data = GridCopyData()
			data.resid = content.itemid
			data.num = content.itemnum
			data.userdata_str = content.userdata
			bp:replaceItem_byGridCopyData(data, content.index)
		end
	end
end

LuaMsgHandler.luaOnTriggerStandReportToHost = function(content)
	if GameVmReport then
		if content.data then
			GameVmReport:Handle2Host(content.data)
		end
	end
end

LuaMsgHandler.luaOperaMatchingMapTeleportToClient = function (content)
	
end

LuaMsgHandler.luaOnAvatarSummonToHost = function (content)
	if not CurWorld then return end
	if not content or not content.objid then
		return
	end
	local player = nil
	if WorldMgr and WorldMgr.getPlayerByUin then
		player = WorldMgr:GetPlayerByUin(content.objid)
	elseif CurWorld and GetWorldActorMgr(CurWorld) then
		player = GetWorldActorMgr(CurWorld):findPlayerByUin(content.objid)
	end
	if player then
		if content.summonid then
			player:avatarSummon(content.summonid)
		end
	end
end

LuaMsgHandler.luaMultiGameMemberOnResumeHandleHost = function(content)
	if not content or not content.player_uin then
		return;
	end
	local player;
	if WorldMgr and WorldMgr.getPlayerByUin then
		player = WorldMgr:GetPlayerByUin(content.player_uin)
	elseif CurWorld and GetWorldActorMgr(CurWorld) then
		player = GetWorldActorMgr(CurWorld):findPlayerByUin(content.player_uin)
	end

	if player and player.setAttribDirty then
		player:setAttribDirty(true)
	end
end

LuaMsgHandler.luaOnBattleReportDataToClient = function (content)
	if content.data then
		if type(content.data) == "string" then
			local tempdata = safe_string2table(content.data)
			if GetInst("BattleReportDataSys") then
				GetInst("BattleReportDataSys"):SetReportData(tempdata)
			end
		end
	end
end

LuaMsgHandler.luaOnGiftShowOnMapToHost = function (content)
	if GetInst("GiftShowOnMapManager") then
		GetInst("GiftShowOnMapManager"):receivedGiftMsgAsHost(content)
	end
end

LuaMsgHandler.luaOnGiftShowOnMapFromHost = function (content)
	if GetInst("GiftShowOnMapManager") then
		GetInst("GiftShowOnMapManager"):receivedGiftMsgAsClient(content)
	end
end

LuaMsgHandler.luaOnBPMathStartSingleGameNotifyToClient = function(content)
	MiniLog("call LuaMsgHandler.luaOnBPMathStartSingleGameNotifyToClient on client")
	-- 悦享赛事地图参与小局游戏上报
	if GetInst("BPCompetitionInterface") then
		GetInst("BPCompetitionInterface"):ReportEnterMapTaskEvt()  -- 上报至悦享赛事
	end

	if GetInst("ActCenterTaskReport") then
		GetInst("ActCenterTaskReport"):ReportBPRaceMapEvent(81)  -- 上报至活动中心
	end
end

LuaMsgHandler.luaOnBPMathSingleResultNotifyToClient = function(content)
	MiniLog("call LuaMsgHandler.luaOnBPMathSingleResultNotifyToClient on client")
	if content.data and type(content.data) == "string" then
		MiniLog(content.data)
		local result_data = safe_string2table(content.data)
		if GetInst("BPCompetitionInterface") then
			GetInst("BPCompetitionInterface"):SetBPMathSingleResult(result_data)
		end

		-- 0-none 1-胜利 2-失败 3-平局
		if result_data.result == 1 then
			-- 悦享赛事地图内小局游戏胜利上报
			if GetInst("ActCenterTaskReport") then
				GetInst("ActCenterTaskReport"):ReportBPRaceMapEvent(82)
			end
		end
	end
end

LuaMsgHandler.luaOnActionChangeOnMap = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):ChangeCurActorMotionToHost(content)
	end
end

LuaMsgHandler.luaOnActionChangeOnMapToClient = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):ChangeCurActorMotion(content)
	end
end

LuaMsgHandler.luaOnActionInvite = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):NoticeActionInvite(content)
	end
end

LuaMsgHandler.luaOnActionInviteHost = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):NoticeOtherActionInvite(content)
	end
end
-- 客户端发起 发送与ai的聊天到主机
LuaMsgHandler.luaClientRequestAITalk= function(content)
	print("luaClientRequestAITalk: recv ai talk content")
	if gFunc_handleClientNPCTalkMessage then
		gFunc_handleClientNPCTalkMessage(1, table2json(content))
	else
		print("luaClientRequestAITalk: no handler function")
	end
end

-- 客户端发起 请求玩家ai的聊天记录
LuaMsgHandler.luaClientRequestGetAITalkHistory = function(content)
	print("luaClientRequestGetAITalkHistory: recv ai talk history reqeust")
	if gFunc_handleClientNPCTalkMessage then
		gFunc_handleClientNPCTalkMessage(2, table2json(content))
	else
		print("luaClientRequestGetAITalkHistory: no handler function")
	end
end

LuaMsgHandler.manualEmitterUpdate = function(content)
	if not CurWorld then
		return;
	end
	if not WorldMgr then
		return;
	end
	if not content then
		return;
	end
	local type = content.type;
	local uin = content.uin;
	local x = content.x;
	local y = content.y;
	local z = content.z;
	local player = WorldMgr:GetPlayerByUin(uin);
	if not player then
		return;
	end
	local pos = WCoord:new_local();
	pos.x = x;
	pos.y = y;
	pos.z = z;
	if CurWorld:isRemoteMode() then
		--主机发客机的
		--客机坐上手持发射器
		if (type == 1) then
			player:mountEmitter(pos);
		elseif (type == 2) then
			--客机从手持发射器下来
			player:disMountEmitterClient(pos);
		elseif (type == 3) then
			--客机使用手持发射器发射
			player:operateManualEmitterClient(pos);
		end
	else
		--客机发主机的
		--使用手持发射器
		if (type == 2) then
			player:disMountEmitter(pos);
		elseif (type == 3) then
			player:operateManualEmitter(pos);
		end
	end
end

LuaMsgHandler.luaOnActionAddPlayer = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):BroadcastPlayerActionStatus(content)
	end
end

LuaMsgHandler.luaOnActionAddPlayerHost = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):SetPlayerActionStatus(content)
	end
end

LuaMsgHandler.luaOnFollowPlayerInfoToClient = function (content)
	if GetInst("PlayerExpressionManager") then
		GetInst("PlayerExpressionManager"):SetPlayerFollowInfo(content)
	end
end

LuaMsgHandler.luaStarStationCargoPickItemToHost = function(content)
	if GetInst("StarStationCargoManager") then
		GetInst("StarStationCargoManager"):HostRequestPickItem(content)
	end
end

LuaMsgHandler.luaStarStationCargoPickItemToClient = function(content)
	if StarStationCargoFrameAutoGen and StarStationCargoFrameAutoGen.ctrl then
		StarStationCargoFrameAutoGen.ctrl:handlePickItem(content)
	end
end

LuaMsgHandler.luaStarStationCargoRemoveBagItemsToHost = function(content)
	if GetInst("StarStationCargoManager") then
		GetInst("StarStationCargoManager"):HostRequestRemoveBagItems(content)
	end
end

_G.teleportInfoFeedBackTabel = { mapid = 0, game_session_id = 0,state = 0}
LuaMsgHandler.luaOnTeleportInfoFeedBack = function(content)
	if type(content.playerid) == "table" then 
		return 
	end
	local uin = AccountManager:getUin() or 0
	local mapid = content.mapid or 0
	local gsd = gFunc_getmd5(uin .. mapid.. os.time()) or 0
	local param = {}
	local ret, standby1, standby2, standby3 = userTaskReportGetWorldParam2(nil, param)
	local reportData = json2table(content.reportData or "")
	local tb = {}
	tb.standby1 = reportData.maptype or 0
	tb.standby2 = gsd
	tb.standby3 = reportData.mapname or ""
	tb.game_session_id = get_game_session_id()
	tb.cid = param.tureWorldId
	tb.extra_id_type = "submap_id"
	tb.extra_id = tostring(mapid)

	local idx = 0
	threadpool:work(function()
		while idx < 20 do 
			threadpool:wait(1)
			idx = idx + 1
			if teleportInfoFeedBackTabel.state == 0 or teleportInfoFeedBackTabel.state == 3 or idx == 20 then
				teleportInfoFeedBackTabel.mapid = mapid
				teleportInfoFeedBackTabel.game_session_id = gsd
				teleportInfoFeedBackTabel.state = 1 -- 0是未发生， 1是请求，2是进入地图，3是退出地图
				return
			end
		end 
	end)
end

LuaMsgHandler.luaStarStationTransferToHost = function(content)
	if GetInst("StarStationCargoManager") then
		GetInst("StarStationCargoManager"):HandleTransferByStarStation(content)
	end
end

LuaMsgHandler.luaStarStationCargoRemoveBagItemsToClient = function(content)
	if StarStationCargoFrameAutoGen and StarStationCargoFrameAutoGen.ctrl then
		StarStationCargoFrameAutoGen.ctrl:handleRemoveBagItems(content)
	end
end

LuaMsgHandler.luaEuipOperateToHost = function(content)
	if GetInst("EquipFactoryInterface") then
		GetInst("EquipFactoryInterface"):HandleEquipOpearteMsgClintToHost(content)
	end
end

LuaMsgHandler.luaEuipOperateToClient = function(content)
	if GetInst("EquipFactoryInterface") then
		GetInst("EquipFactoryInterface"):HandleEquipOpearteMsgHostToClient(content)
	end
end

LuaMsgHandler.luaOpenTransferStationUIToClient = function(content)
	if Service.World then
		Service.World:ShowTransferView(content.playerid, content.mapid, content.srcGateId)
	end
end

LuaMsgHandler.luaReceiveModBlockDataEx = function(content)
	if Service.World then
		Service.World:ReceiveModBlockDataEx(content)
	end
end

-- 客机通知主机与好友游玩
LuaMsgHandler.luaRecvFriendPlayTimeToHost = function(content)
	if OnRecvFriendPlayTime then
		OnRecvFriendPlayTime(content)
	end
end

LuaMsgHandler.luaRecvFriendPlay30001TimeToHost = function(content)
--	MiniLog("luaRecvFriendPlay30001TimeToHost", content)
	if OnRecvFriendPlay30001Time then
		MiniLog("call OnRecvFriendPlay30001Time")
	    OnRecvFriendPlay30001Time(content)
	end
end


LuaMsgHandler.luaOnFishRankMatchSuc = function(content)
	local uin = tonumber(content.uin)
	if FishRankService_EnterWorld then
		FishRankService_EnterWorld(uin)
	end
	MiniLog("luaOnFishRankMatchSuc content.uin="..content.uin)
end

LuaMsgHandler.luaOnFishRankMatch = function(content)
	if FishRankService_ReqMatch then
		FishRankService_ReqMatch()
	end
	MiniLog("FishRankService_ReqMatch")
end

-- 房间内试穿装扮-客机上报
LuaMsgHandler.luaTryOnDressupMapToHost = function(content)
	local mapStoreInst = GetInst("MapStoreInterface")
	if mapStoreInst then 
		mapStoreInst:TryOnDressUpToHost(content)
	end 
end

-- 房间内试穿装扮-主机广播
LuaMsgHandler.luaTryOnDressupMapFromHost = function(content)
	local mapStoreInst = GetInst("MapStoreInterface")
	if mapStoreInst then 
		mapStoreInst:TryOnDressupByUin(content)
	end 
end

-- 主机向客机同步玩家列表数据 (新加入玩家)
LuaMsgHandler.luaTryOnDressupMapToClient = function(content)
	local mapStoreInst = GetInst("MapStoreInterface")
	if mapStoreInst then 
		mapStoreInst:UpdataDressupByInfo(content)
	end 
end

LuaMsgHandler.luaEquipGunComponentToHost = function(content)
	MiniLog("luaEquipGunComponentToHost", FormatPrint(2, content,"content"))
	if not WorldMgr or not content.player_uin then
		return
	end
	local uin = content.player_uin
	local player = WorldMgr:GetPlayerByUin(uin)
	if not player then
		return
	end
	local ret = EquipGunComponent(player, content.origin_gridindex, content.target_gridindex, content.cur_index)
	if not ret then
		player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 700305)  -- 无法装备该配件
	else
		player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 700302)  -- 配件已装备
	end
end

LuaMsgHandler.luaUnEquipGunComponentToHost = function(content)
	MiniLog("luaEquipGunComponentToHost", FormatPrint(2, content,"content"))
	if not WorldMgr or not content.player_uin then
		return
	end
	local uin = content.player_uin
	local player = WorldMgr:GetPlayerByUin(uin)
	if not player then
		return
	end
	local ret = UnEquipGunComponent(player, content.gun_gridindex, content.com_index, content.moveto_gridindex)
	if not ret then
		player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 700306)  -- 无法卸载该配件
	else
		player:notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 700303)  -- 配件已卸载
	end
end

LuaMsgHandler.luaGunComponentChangeToClient = function(content)
	MiniLog("luaGunComponentChangeToClient", FormatPrint(2, content,"content"))
	if not WorldMgr or not content.gun_gridindex then
		return
	end
	local player = WorldMgr:GetPlayerByUin(content.player_uin)
	if not player then
		return
	end

	OnGunComponentChange(player, content.gun_gridindex)
end

------------- lua msg handle ------------------------
function LuaMsgHandler:SubscribeClientHandle()
	-- 地图传送
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTI_MAP_TELEPORT_TOCLIENT, LuaMsgHandler.luaOnMapTeleportClient)
	-- 地图传送邀请
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.TELEPORT_SEND_INVITE_TOCLIENT, LuaMsgHandler.luaOnTeleportInvitedToClient)
	-- 地图传送邀请确认
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.TELEPORT_INVITE_CONFIRM_TOCLIENT, LuaMsgHandler.luaOnTpInviteConfirmToClient)
	--开发者商城-主机通知客机提取道具状态
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT, LuaMsgHandler.luaDeveloperstoreExtraStoreItemToClient)
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.ACTAWARD_GAIN_ITEM_TOCLIENT, LuaMsgHandler.luaActAwardGainItemToClient)
	-- codeby fym 2022/04/15 商城-仓库-游戏内道具提取-反外挂逻辑处理：主机扣除道具成功之后通知客机提取道具状态
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.SHOP_EXTRASTOREITEM_TOCLIENT, LuaMsgHandler.ShopExtraStoreItemState_ToClient)
	
	-- 普通联机房间基础信息更改
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTII_NOR_ROOM_INFO_CHANGED_TOCLIENT, LuaMsgHandler.luaOnMultNorRoomInfoChangedToClient)
	-- 云服主机向客机同步触发器运行信息
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.TRIGGER_DEBUG_INFO_TOCLIENT, LuaMsgHandler.luaOnCloudTriggerDebugInfoToClient)
	-- 主机下发通知客机上报
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.REPORT_CHEAT_INFO_TOCLIENT, LuaMsgHandler.luaOnHostAskClientReportCheat)
	-- 收到云服主机分发的基础信息更改通知
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTII_CLOUD_ROOM_INFO_CHANGED_TOCLIENT, LuaMsgHandler.luaOnMultCloudRoomInfoChangedToClient)

	
	-- 云服拥有者向云服主机发送基础信息更改请求
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTII_CLOUD_ROOM_INFO_CHANGED_TOHOST, LuaMsgHandler.luaOnMultCloudRoomInfoChangedToHost)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.OTHER_VOTE_KICK_OFF_PLAYER_TOCLIENT,LuaMsgHandler.luaOnCloudOtherPlayerVoteKickOffPlayerToClient)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.VOTE_KICK_OFF_PLAYER_TOCLIENT, LuaMsgHandler.luaOnCloudKickOffPlayerToClient)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.VOTE_KICK_OFF_PLAYER_END_TOCLIENT, LuaMsgHandler.luaOnCloudKickOffPlayerEndToClient)
	--任务上报
	SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.PLAYER_REPORT_TASK, LuaMsgHandler.userTaskReport)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.MOB_SCORPION_HIDE_CHANGE, LuaMsgHandler.luaOnMobChangeScorpionHideStatus)
	-- 云服主机向客机获取运行信息
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.TRIGGER_STANDREPORTT_TOCLIENT, LuaMsgHandler.luaOnTriggerStandReportEventInfoToClient)
	--codeby jeff 2022/8/5 同步生物信息(最大值)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.ACTOR_SET_ATTR_CHANCE, LuaMsgHandler.luaOnActorAttrSetChange)
	-- 同步studioparam
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.SYNC_STUDIO_PARAM_2_CLIENT, LuaMsgHandler.luaOnRecvStudioParam)
	-- 兑换星星结果
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.COIN_CONVERT_STAR_TOCLIENT, LuaMsgHandler.luaOnCoinConvertStarToClient)
	
	-- 通知客机 报名传送 服务器发送
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.TELEPORT_REGISTER_TOCLIENT, LuaMsgHandler.luaOperaMatchingMapTeleportToClient)
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.BATTLE_REPORT_DATA_TOCLIENT, LuaMsgHandler.luaOnBattleReportDataToClient)
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTII_CLOUD_NOTFIY_MEMBER_TEAM_DESC_TOHOST, LuaMsgHandler.luaOnMultCloudNotifyMemberTeamDescToHost)
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTII_CLOUD_NOTFIY_MEMBER_TEAM_DESC_TOCLIENT, LuaMsgHandler.luaOnMultCloudNotifyMemberTeamDescToClient)

	-- 游商交易
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.NPC_TRADE_OPENUI, LuaMsgHandler.luaOnRegisterNpcTraderOpenUI);
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.NPC_TRADE_BUY_RESPONE, LuaMsgHandler.luaOnRegisterNpcTradeBuyRespone);
	
	-- 云服通知客机 悦享赛事主推地图：迷你枪战 参与小局游戏数据同步
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.BPMATH_START_SINGLE_GAME_NOTIFY_TOCLIENT, LuaMsgHandler.luaOnBPMathStartSingleGameNotifyToClient)
	-- 云服通知客机 悦享赛事主推地图：迷你枪战 小局结束数据同步 
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.BPMATH_SINGLE_BATTLE_RESULT_NOTIFY_TOCLIENT, LuaMsgHandler.luaOnBPMathSingleResultNotifyToClient)

	-- 工匠台写入获取记录
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.BLOCK_TOOLBOX_HISTORY, LuaMsgHandler.luaOnRegisterToolboxHistory);

	-- 技能编辑 显示ScreenUI
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.SKILL_TIMELINE_SHOWUI_TOCLIENT, LuaMsgHandler.luaOnShowScreenUIToClient);

	-- 手持发射器
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.MANUAL_EMITTER, LuaMsgHandler.manualEmitterUpdate);

	--接收对话返回
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.SYNC_NPC_AI_TALK_SEND_MESSAGE_RESPONE, LuaMsgHandler.syncNpcAiTalkSendMessageResponeFunc)
	--接收获取对话返回
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.SYNC_NPC_AI_TALK_GET_MESSAGE_RESPONE, LuaMsgHandler.syncNpcAiTalkGetMessageResponeFunc)
	--接收云服公告推送
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.SYNC_CLOUD_ANNOUNCEMENT, LuaMsgHandler.syncCloudAnnoucementFunc)
	-- 星链终端仓库取物品结果通知客户端
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.STARSTATION_CARGO_PICKITEM_TOCLIENT, LuaMsgHandler.luaStarStationCargoPickItemToClient)

	-- 接受请求跳转收到得反馈
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.CLIENT_REQUIRE_TELEPORT_RECEIVE_FEEDBACK, LuaMsgHandler.luaOnTeleportInfoFeedBack)
end

function LuaMsgHandler:SubscribeHostHandle()
	-- 地图传送
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.MULTI_MAP_TELEPORT_TOHOST, LuaMsgHandler.luaMultiMapTeleportHost)
	-- 地图传送邀请
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.TELEPORT_SEND_INVITE_TOHOST, LuaMsgHandler.luaOnTeleportInvitedToHost)
	-- 地图传送邀请确认
	SandboxLuaMsg:SubscibeMsgHandle(tbGlobalMsg.TELEPORT_INVITE_CONFIRM_TOHOST, LuaMsgHandler.luaOnTpInviteConfirmToHost)

	-- codeby fym 2022/01/21 心愿商人-反外挂逻辑处理：客机通知主机发奖
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SHOP_ADNPC_REWARD_TOHOST, LuaMsgHandler.luaShopAdNpcHost_RewardHandle)
	-- codeby fym 2022/03/07 商城-仓库-游戏内道具提取-反外挂逻辑处理：客机通知主机提取道具
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SHOP_EXTRASTOREITEM_TOHOST, LuaMsgHandler.luaShopExtraStoreItem_RewardHandle)
	--开发者商城-客机通知主机提取道具

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.DEVELOPERSTORE_EXTRASTOREITEM_TOHOST, LuaMsgHandler.luaDeveloperstoreExtraStoreItemToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.OPEN_TRIGGER_LOG_TOHOST, LuaMsgHandler.luaOnCloudRequestStartTriggerDebugInfoToHost)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.ACTAWARD_GAIN_ITEM_TOHOST, LuaMsgHandler.luaActAwardGainItemToHost)

	-- 迷你币兑换星星
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.COIN_CONVERT_STAR_TOHOST, LuaMsgHandler.luaOnCoinConvertStar)

	--云服踢人
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.VOTE_KICK_OFF_PLAYER_START_TOHOST, LuaMsgHandler.luaOnCloudRequestStartKickOffPlayerToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.VOTE_KICK_OFF_PLAYER_TOHOST, LuaMsgHandler.luaOnCloudRequestVoteKickOffPlayerToHost)
	--冒险客机通知主机普通商人动画
	SandboxLuaMsg:SubscibeMsgHandle(SANDBOX_LUAMSG_NAME.Survive.DESERT_BUSSINESSMAN_DEAL, LuaMsgHandler.desertBuinessmanDealHandle)

	--房间内送礼（客机通知主机）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.GIFT_SHOW_ON_MAP_TOHOST, LuaMsgHandler.luaOnGiftShowOnMapToHost)

	--房间内送礼（主机广播）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.GIFT_SHOW_ON_MAP_FROMHOST, LuaMsgHandler.luaOnGiftShowOnMapFromHost)

	--坐骑技能播放特效客机同步主机
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.HORSE_SKILL_PLAY_EFFECT_TOHOST, LuaMsgHandler.luaOnHorsePlayEffectToHost)
		--客机同步设备信息到主机
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.TRIGGER_STANDREPORTT_TOHOST, LuaMsgHandler.luaOnTriggerStandReportToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.DRIFTBOTTLE_GETHOST, LuaMsgHandler.luaGetDriftBottleHandleHost);
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.REPLACE_GRID_WITH_USERDATASTR, LuaMsgHandler.luaReplaceGridWithUserdataStr)
	
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.HORSE_SKILL_ERROR_CODE_TOCLIENT, LuaMsgHandler.luaOnHorseSkillErrorCode)
	--皮肤召唤客机通知主机
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.AVATAR_SUMMON_TOHOST, LuaMsgHandler.luaOnAvatarSummonToHost)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.MULTII_GAME_MEMBER_ON_RESUME_TOHOST, LuaMsgHandler.luaMultiGameMemberOnResumeHandleHost);

	-- 通知主机 报名传送 客户端发送
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.TELEPORT_REGISTER_TOHOST, LuaMsgHandler.luaOnRegisterTeleportToHost);
	
	--地图内动作切换（客机通知主机）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.ACTION_CHANGE_ON_MAP_TOHOST, LuaMsgHandler.luaOnActionChangeOnMap)
	--地图内动作切换（主机广播）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.ACTION_CHANGE_ON_MAP_FROMHOST, LuaMsgHandler.luaOnActionChangeOnMapToClient)

	--互动动作通知（客机通知主机）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.ACTION_INVITE_TOHOST, LuaMsgHandler.luaOnActionInvite)
	--互动动作通知（主机广播）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.ACTION_INVITE_FROMHOST, LuaMsgHandler.luaOnActionInviteHost)

	--动作参与通知（客机通知主机）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.ACTION_ADD_TOHOST, LuaMsgHandler.luaOnActionAddPlayer)
	--动作参与通知（主机广播）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.ACTION_ADD_FROMHOST, LuaMsgHandler.luaOnActionAddPlayerHost)
	--主机向客机同步跟随动作玩家列表
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.FOLLOW_INFO_TOCLIENT, LuaMsgHandler.luaOnFollowPlayerInfoToClient)

	-- 游商商店交易
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.NPC_TRADE_BUY, LuaMsgHandler.luaOnRegisterNpcTraderBuy);
	-- 星星惩罚
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SET_STAR_PINISH, LuaMsgHandler.luaOnStarPunishToHost);
	
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SYNC_NPC_AI_TALK_SEND_MESSAGE, LuaMsgHandler.luaClientRequestAITalk)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.SYNC_NPC_AI_TALK_GET_MESSAGE, LuaMsgHandler.luaClientRequestGetAITalkHistory)		
	-- 手持发射器
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.MANUAL_EMITTER, LuaMsgHandler.manualEmitterUpdate);

	-- 通知主机取星链终端仓库物品
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.STARSTATION_CARGO_PICKITEM_TOHOST, LuaMsgHandler.luaStarStationCargoPickItemToHost)
	-- 通知主机去除背包物品
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.STARSTATION_CARGO_REMOVEBAGITEMS_TOHOST, LuaMsgHandler.luaStarStationCargoRemoveBagItemsToHost)
	-- 通知主机客机通过星站穿越到此图
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.STARSTATION_TRANSFER_TOHOST, LuaMsgHandler.luaStarStationTransferToHost)
	-- 通知客机删除背包物品结果
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.STARSTATION_CARGO_REMOVEBAGITEMS_TOCLIENT, LuaMsgHandler.luaStarStationCargoRemoveBagItemsToClient)
	-- 通知主机操作工作台
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.EQUIP_OPERATE_TO_HOST, LuaMsgHandler.luaEuipOperateToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.Survive.EQUIP_OPERATE_TO_CLIENT, LuaMsgHandler.luaEuipOperateToClient)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.OPEN_TRANSFERSTATION_UI, LuaMsgHandler.luaOpenTransferStationUIToClient)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.MODBLOCK_CONTAINER_EXTRA_DATA, LuaMsgHandler.luaReceiveModBlockDataEx)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.FRIEND_PLAY_TIME_TOHOST, LuaMsgHandler.luaRecvFriendPlayTimeToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.ROOM.FRIEND_PLAY_30001_TIME_TOHOST, LuaMsgHandler.luaRecvFriendPlay30001TimeToHost)

	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.FISH_RANK_MATCH, LuaMsgHandler.luaOnFishRankMatch);
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.FISH_RANK_MATCH_SUC, LuaMsgHandler.luaOnFishRankMatchSuc);
	
	-- 房间内试穿皮肤和装扮（客机通知主机）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.TRYON_DRESSUP_MAP_TOHOST, LuaMsgHandler.luaTryOnDressupMapToHost)
	-- 房间内试穿皮肤和装扮（主机广播）
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.TRYON_DRESSUP_MAP_FROMHOST, LuaMsgHandler.luaTryOnDressupMapFromHost)
	-- 主机向客机同步玩家列表数据 (新加入玩家)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.BUZZ.TRYON_DRESSUP_MAP_TOCLIENT, LuaMsgHandler.luaTryOnDressupMapToClient)

	-- 枪械配件操作
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.EQUIP_GUN_COMPONENT_TOHOST, LuaMsgHandler.luaEquipGunComponentToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.UN_EQUIP_GUN_COMPONENT_TOHOST, LuaMsgHandler.luaUnEquipGunComponentToHost)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.CLOUD.GUN_COMPONENT_CHANGE_TOCLIENT, LuaMsgHandler.luaGunComponentChangeToClient)
end

function LuaMsgHandler:SubscribeTrackingPlayersHandle()
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.MOB_SADDLE_CHANGE_TOTRACKINGPLAYERS, LuaMsgHandler.luaOnMobChangeSaddleToTrackingPlayers)
	SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.GLOBAL.MOB_SCORPION_NECKLACE_CHANGE, LuaMsgHandler.luaOnMobChangeScorpionNecklace)		
end

function LuaMsgHandler:SubscribeDisplayboardHandle()
	print("LuaMsgHandler:SubscribeDisplayboardHandle~~~~~~~~~~~~~~~~~~~~~~~~~~~");
	-- 收主机视频时间
    SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.CONST_NET_EVENT_H2C_RSP_VIDEO_TIME, function(...)
		print("LuaMsgHandler:SubscribeDisplayboardHandle~~~~~~~~~~~~~~~~~~~~~~~~~~~111");
		if GetInst("DisplayBoardNetManager") then
        	GetInst("DisplayBoardNetManager"):Host2ClientVideoTime(...)
		end
    end)
    -- 收客机的视频时间请求
    SandboxLuaMsg:SubscibeMsgHandle(_G.SANDBOX_LUAMSG_NAME.UGC.CONST_NET_EVENT_C2H_REQ_VIDEO_TIME, function(...)
		if GetInst("DisplayBoardNetManager") then
        	GetInst("DisplayBoardNetManager"):Client2HostVideoTime(...)
		end
    end)
end