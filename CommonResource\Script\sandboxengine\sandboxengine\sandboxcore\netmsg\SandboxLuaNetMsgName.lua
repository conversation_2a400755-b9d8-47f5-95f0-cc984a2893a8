_G.SANDBOX_LUAMSG_NAME = 
{
    --可以按业务部门分类
    ["UGC"] =
    {
        WDESC_EX_TO_CLIENT = "WDESC_EX_TO_CLIENT",
        WGLOBAL_EX_TO_CLIENT = "WGLOBAL_EX_TO_CLIENT",
        -- ¿Í»úÇëÇóÍ¬²½
        CONST_NET_EVENT_C2H_REQ_VIDEO_TIME = "CONST_NET_EVENT_C2H_REQ_VIDEO_TIME",
        -- ·¢ËÍ¸ø¿Í»úÊÓÆµ½ø¶È
        CONST_NET_EVENT_H2C_RSP_VIDEO_TIME = "CONST_NET_EVENT_H2C_RSP_VIDEO_TIME",
        TRIGGER_OPEN_DEVELOPSTORE = "TRIGGER_OPEN_DEVELOPSTORE",
	    TRIGGER_OPEN_DEVELOPSTORE_NEW = "TRIGGER_OPEN_DEVELOPSTORE_NEW",
        TRIGGER_STANDREPORTT_TOCLIENT = "TRIGGER_STANDREPORTT_TOCLIENT", -- 开发者接口上报自定义埋点数据
        TRIGGER_STANDREPORTT_TOHOST = "TRIGGER_STANDREPORTT_TOHOST", -- 开发者接口上报自定义埋点数据
        TRIGGER_OPEN_DEVELOPSTORE_TAB = "TRIGGER_OPEN_DEVELOPSTORE_TAB", --打开开发者商店分类
        --mod传送门打开ui
        OPEN_TRANSFERSTATION_UI = "OPEN_TRANSFERSTATION_UI",
        MODBLOCK_CONTAINER_EXTRA_DATA = "MODBLOCK_CONTAINER_EXTRA_DATA",
        TRIGGER_CLIENTPOSTEVENT_TOHOST = "TRIGGER_CLIENTPOSTEVENT_TOHOST" -- 客机上报触发器事件
    },
    ["GLOBAL"] =
    {
        -- toclient -------------------------------
        -- 地图传送
        MULTI_MAP_TELEPORT_TOCLIENT = "MULTI_MAP_TELEPORT_TOCLIENT",

        -- tohost ----------------------------------
        -- 地图传送
        MULTI_MAP_TELEPORT_TOHOST = "MULTI_MAP_TELEPORT_TOHOST",

        -- 校验数据上报
        CHECK_DATA_UPLOAD_TOHOST = "CHECK_DATA_UPLOAD_TOHOST",

        -- codeby fym 2022/01/21 心愿商人-反外挂逻辑处理：客机通知主机发奖
        SHOP_ADNPC_REWARD_TOHOST = "SHOP_ADNPC_REWARD_TOHOST",
        -- codeby fym 2022/03/07 商城-仓库-游戏内道具提取-反外挂逻辑处理：客机通知主机提取道具
        SHOP_EXTRASTOREITEM_TOHOST = "SHOP_EXTRASTOREITEM_TOHOST",
        -- codeby fym 2022/04/15 商城-仓库-游戏内道具提取-反外挂逻辑处理：主机扣除道具成功之后通知客机提取道具状态
        SHOP_EXTRASTOREITEM_TOCLIENT = "SHOP_EXTRASTOREITEM_TOCLIENT",
         
        --开发者商城-客机通知主机提取道具
        DEVELOPERSTORE_EXTRASTOREITEM_TOHOST = "DEVELOPERSTORE_EXTRASTOREITEM_TOHOST",
        --开发者商城-主机通知客机提取道具状态
        DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT = "DEVELOPERSTORE_EXTRASTOREITEM_TOCLIENT",

        ACTAWARD_GAIN_ITEM_TOHOST = "ACTAWARD_GAIN_ITEM_TOHOST",
        ACTAWARD_GAIN_ITEM_TOCLIENT = "ACTAWARD_GAIN_ITEM_TOCLIENT",

        -- 兑换星星
        COIN_CONVERT_STAR_TOHOST = "COIN_CONVERT_STAR_TOHOST",
        COIN_CONVERT_STAR_TOCLIENT = "COIN_CONVERT_STAR_TOCLIENT",

        -- 地图传送邀请
        TELEPORT_SEND_INVITE_TOHOST = "TELEPORT_SEND_INVITE_TOHOST",
        TELEPORT_SEND_INVITE_TOCLIENT = "TELEPORT_SEND_INVITE_TOCLIENT",
        
        -- 地图传送邀请确认
        TELEPORT_INVITE_CONFIRM_TOHOST = "TELEPORT_INVITE_CONFIRM_TOHOST",
        TELEPORT_INVITE_CONFIRM_TOCLIENT = "TELEPORT_INVITE_CONFIRM_TOCLIENT",

        -- 普通联机房间基础信息更改
        MULTII_NOR_ROOM_INFO_CHANGED_TOCLIENT = "MULTII_NOR_ROOM_INFO_CHANGED_TOCLIENT",
	
	
        --同步生物信息
        ACTOR_SET_ATTR_CHANCE = "ACTOR_SET_ATTR_TOTRACKINGPLAYERS",

        MULTII_CLOUD_ROOM_INFO_CHANGED_TOHOST = "MULTII_CLOUD_ROOM_INFO_CHANGED_TOHOST",
        MULTII_CLOUD_ROOM_INFO_CHANGED_TOCLIENT = "MULTII_CLOUD_ROOM_INFO_CHANGED_TOCLIENT",


        --生物同步saddle显示情况
        MOB_SADDLE_CHANGE_TOTRACKINGPLAYERS = "MOB_SADDLE_CHANGE_TOTRACKINGPLAYERS",
        MOB_SCORPION_NECKLACE_CHANGE = "MOB_SCORPION_NECKLACE_CHANGE",
        MOB_SCORPION_HIDE_CHANGE = "MOB_SCORPION_HIDE_CHANGE",

        -- stuio param同步
        SYNC_STUDIO_PARAM_2_CLIENT = "SYNC_STUDIO_PARAM_2_CLIENT",
        
        REPLACE_GRID_WITH_USERDATASTR = "REPLACE_GRID_WITH_USERDATASTR",
        SYNC_NPC_AI_TALK_SEND_MESSAGE = "SYNC_NPC_AI_TALK_SEND_MESSAGE",
        SYNC_NPC_AI_TALK_SEND_MESSAGE_RESPONE = "SYNC_NPC_AI_TALK_SEND_MESSAGE_RESPONE",

        SYNC_NPC_AI_TALK_GET_MESSAGE = "SYNC_NPC_AI_TALK_GET_MESSAGE",
        SYNC_NPC_AI_TALK_GET_MESSAGE_RESPONE = "SYNC_NPC_AI_TALK_GET_MESSAGE_RESPONE",

        SYNC_CLOUD_ANNOUNCEMENT = "SYNC_CLOUD_ANNOUNCEMENT",
        MULTII_GAME_MEMBER_ON_RESUME_TOHOST= "MULTII_GAME_MEMBER_ON_RESUME_TOHOST",

        --同步战报信息
        BATTLE_REPORT_DATA_TOCLIENT = "BATTLE_REPORT_DATA_TOCLIENT",
        MULTII_CLOUD_NOTFIY_MEMBER_TEAM_DESC_TOHOST = "MULTII_CLOUD_NOTFIY_MEMBER_TEAM_DESC_TOHOST",
        MULTII_CLOUD_NOTFIY_MEMBER_TEAM_DESC_TOCLIENT = "MULTII_CLOUD_NOTFIY_MEMBER_TEAM_DESC_TOCLIENT",

        -- code by fym 2023/12/14 同步悦享赛事-迷你枪战地图小局结算数据给云服客机
        BPMATH_SINGLE_BATTLE_RESULT_NOTIFY_TOCLIENT = "BPMATH_SINGLE_BATTLE_RESULT_NOTIFY_TOCLIENT",
        -- code by fym 2023/12/14 云服通知客机悦享赛事-迷你枪战地图参与小局游戏
        BPMATH_START_SINGLE_GAME_NOTIFY_TOCLIENT = "BPMATH_START_SINGLE_GAME_NOTIFY_TOCLIENT",
        --code by renjie 用于客机发送信息让主机处理星星惩罚扣除星星
        SET_STAR_PINISH = "SET_STAR_PINISH",

        --钓鱼大赛-客机通知主机匹配成功
        FISH_RANK_MATCH_SUC = "FISH_RANK_MATCH_SUC",
        --钓鱼大赛-主机通知客机进行匹配
        FISH_RANK_MATCH = "FISH_RANK_MATCH",
    },

    ["BUZZ"] = --商业化
    {
        -- 玩家请求同步皮肤信息
        REQ_WEAPON_SKIN_INFO_HOST_CLOUD = "REQ_WEAPON_SKIN_INFO_HOST_CLOUD",
        HORSE_SKILL_PLAY_EFFECT_TOHOST = "HORSE_SKILL_PLAY_EFFECT_TOHOST",
        HORSE_SKILL_ERROR_CODE_TOCLIENT = "HORSE_SKILL_ERROR_CODE_TOCLIENT",
        AVATAR_SUMMON_TOHOST = "AVATAR_SUMMON_TOHOST",
        GIFT_SHOW_ON_MAP_TOHOST = "GIFT_SHOW_ON_MAP_TOHOST", --房间内送礼（客机通知主机）
        GIFT_SHOW_ON_MAP_FROMHOST = "GIFT_SHOW_ON_MAP_FROMHOST", --房间内送礼（主机广播）
        INDENTURES_PLAY_TOGETHER = "INDENTURES_PLAY_TOGETHER", --师徒同玩消息
        
        ACTION_CHANGE_ON_MAP_TOHOST = "ACTION_CHANGE_ON_MAP_TOHOST", --房间内动作切换（客机通知主机）
        ACTION_CHANGE_ON_MAP_FROMHOST = "ACTION_CHANGE_ON_MAP_FROMHOST", --房间内动作切换（主机广播）

        ACTION_INVITE_TOHOST = "ACTION_INVITE_TOHOST", --互动动作通知（客机通知主机）
        ACTION_INVITE_FROMHOST = "ACTION_INVITE_FROMHOST", --互动动作通知（主机广播）

        ACTION_ADD_TOHOST = "ACTION_ADD_TOHOST", --参与单人动作通知（客机通知主机）
        ACTION_ADD_FROMHOST = "ACTION_ADD_FROMHOST", --参与单人动作通知（主机广播）
        FOLLOW_INFO_TOCLIENT = "FOLLOW_INFO_TOCLIENT", --主机向客机同步跟随动作玩家列表
        
        TRYON_DRESSUP_MAP_TOHOST   = "TRYON_DRESSUP_MAP_TOHOST",      -- 房间内试穿皮肤和装扮（客机通知主机）
        TRYON_DRESSUP_MAP_FROMHOST = "TRYON_DRESSUP_MAP_FROMHOST",    -- 房间内试穿皮肤和装扮（主机广播）
        TRYON_DRESSUP_MAP_TOCLIENT = "TRYON_DRESSUP_MAP_TOCLIENT",    -- 主机向客机同步玩家列表数据

    },
    ["CLOUD"] = -- 云服
    {
        TRIGGER_DEBUG_INFO_TOCLIENT = "TRIGGER_DEBUG_INFO_TOCLIENT", -- 云服主机向客机同步触发器运行信息
        OPEN_TRIGGER_LOG_TOHOST = "OPEN_TRIGGER_LOG_TOHOST", -- 客机请求云服主机开始同步触发器日志
        REPORT_CHEAT_INFO_TOCLIENT = "REPORT_CHEAT_INFO_TOCLIENT",   -- 主机通知客机上报大数据
        TELEPORT_REGISTER_TOHOST = "TELEPORT_REGISTER_TOHOST",  -- 报名传送
        TELEPORT_REGISTER_TOCLIENT = "TELEPORT_REGISTER_TOCLIENT",  -- 报名传送
	    OPEN_STUDIO_CLOUD_SERVER_PERFORMANCE_TOHOST = "OPEN_STUDIO_CLOUD_SERVER_PERFORMANCE_TOHOST", --订阅或取消studio云服主机性能数据
        SYNC_STUDIO_CLOUD_SERVER_PERFORMANCE_TOCLIENT = "SYNC_STUDIO_CLOUD_SERVER_PERFORMANCE_TOCLIENT", --同步studio云服主机性能数据
        CLIENT_REQUIRE_TELEPORT_RECEIVE_FEEDBACK = "CLIENT_REQUIRE_TELEPORT_RECEIVE_FEEDBACK", -- 客服请求传送接收反馈

        -- 枪械配件操作
        EQUIP_GUN_COMPONENT_TOHOST = "EQUIP_GUN_COMPONENT_TOHOST",  -- 客机装备枪械配件
        UN_EQUIP_GUN_COMPONENT_TOHOST = "UN_EQUIP_GUN_COMPONENT_TOHOST",  -- 客机卸载枪械配件

        GUN_COMPONENT_CHANGE_TOCLIENT = "GUN_COMPONENT_CHANGE_TOCLIENT",  -- 通知客机枪械配件变化
    },
    ["ROOM"] =  -- 房间
    {
        VOTE_KICK_OFF_PLAYER_START_TOHOST = "VOTE_KICK_OFF_PLAYER_START_TOHOST", --投票发起踢人
        VOTE_KICK_OFF_PLAYER_TOCLIENT = "VOTE_KICK_OFF_PLAYER_TOCLIENT", --投票踢人
        VOTE_KICK_OFF_PLAYER_TOHOST = "VOTE_KICK_OFF_PLAYER_TOHOST", --投票踢人
        VOTE_KICK_OFF_PLAYER_END_TOCLIENT = "VOTE_KICK_OFF_PLAYER_END_TOCLIENT", --投票结束
        OTHER_VOTE_KICK_OFF_PLAYER_TOCLIENT = "OTHER_VOTE_KICK_OFF_PLAYER_TOCLIENT", -- 广播其他玩家投票

        FRIEND_PLAY_TIME_TOHOST = "FRIEND_PLAY_TIME_TOHOST", --好友玩耍时间
        FRIEND_PLAY_30001_TIME_TOHOST = "FRIEND_PLAY_30001_TIME_TOHOST", --好友玩耍时间
    },
    ["Survive"] = 
    {
        PLAYER_ENABLE_SHOW_POS_TOOTHER = "PLAYER_ENABLE_SHOW_POS_TOOTHER",
        PLAYER_CLOSE_UI = "PLAYER_CLOSE_UI",
        SHOW_EXCHANGE_ITEM_ICON = "SHOW_EXCHANGE_ITEM_ICON",
        PLAYER_REPORT_TASK = "PLAYER_REPORT_TASK",
        DESERT_BUSSINESSMAN_DEAL = "DESERT_BUSSINESSMAN_DEAL",
        SHOW_HEAD_ICON_BY_PATH = "SHOW_HEAD_ICON_BY_PATH",
        SYN_SLEEP_STATE = "SYN_SLEEP_STATE",
        ACOTRBODY_SHOW = "ACOTRBODY_SHOW",
        SURVIVE_REPORT = "SURVIVE_REPORT",
        DRIFTBOTTLE_GET = "DRIFTBOTTLE_GET",
        DRIFTBOTTLE_GETHOST = "DRIFTBOTTLE_GETHOST",
        MANUAL_EMITTER = "MANUAL_EMITTER",
        VOID_DRAGON_FLOWER_ATTACK = "VOID_DRAGON_FLOWER_ATTACK",

        -- 冒险游商
        NPC_TRADE_OPENUI        = "NPC_TRADER_OPENUI",
        NPC_TRADE_BUY           = "NPC_TRADER_BUY",
        NPC_TRADE_BUY_RESPONE   = "NPC_TRADER_BUY_RESPONE",

        --工匠台
        BLOCK_TOOLBOX_HISTORY   = "BLOCK_TOOLBOX_HISTORY",

        --技能编辑
        SKILL_TIMELINE_SHOWUI_TOCLIENT   = "SKILL_TIMELINE_SHOWUI_TOCLIENT",

        --星链终端取物品
        STARSTATION_CARGO_PICKITEM_TOCLIENT = "STARSTATION_CARGO_PICKITEM_TOCLIENT",
        STARSTATION_CARGO_PICKITEM_TOHOST = "STARSTATION_CARGO_PICKITEM_TOHOST",
        STARSTATION_CARGO_REMOVEBAGITEMS_TOHOST = "STARSTATION_CARGO_REMOVEBAGITEMS_TOHOST",
        -- 星站穿越
        STARSTATION_TRANSFER_TOHOST = "STARSTATION_TRANSFER_TOHOST",
        STARSTATION_CARGO_REMOVEBAGITEMS_TOCLIENT = "STARSTATION_CARGO_REMOVEBAGITEMS_TOCLIENT",
        -- 枪/装备工作台
        EQUIP_OPERATE_TO_HOST = "EQUIP_OPERATE_TO_HOST",
        EQUIP_OPERATE_TO_CLIENT = "EQUIP_OPERATE_TO_CLIENT",
        --城镇搜索
        CITY_SEARCH_CLIENT = "CITY_SEARCH_CLIENT",
        --mod藏宝图使用
        TREASURE_USE_CLIENT = "TREASURE_USE_CLIENT",
        --副本建筑标记
        SPECIAL_BUILD_MARK = "SPECIAL_BUILD_MARK"
    }
}