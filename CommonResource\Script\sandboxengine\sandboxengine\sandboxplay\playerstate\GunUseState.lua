-- 参数变量
local m_StateID = 16
local m_ReloadStartMark = -1
local m_FireStartMark = -1
local m_PullingStartMark = -1
local m_bHavePulled = false
local m_IdleStateReady = false
local m_FireOnceAnimDuration = 0
local m_GunCoolDown = 0
local m_GunDef = nil
local m_FireStartTime = -1

local m_Host = nil
local m_CurToolID = 0
local m_CurShortcut = nil
local m_NeedReload = false
local m_ReloadTime = 0

local VK_LBUTTON = 1
local VK_RBUTTON = 2


-- 获取弹夹数量
local function getMagazines()
    local fMagazines = m_Host:getGunLogical():getMaxMagazines()

    return fMagazines
end


-- 增加子弹射速
local function getFiringrate()
    local reloadTime = m_ReloadTime
	
    local resultObj = SandboxLua.eventDispatcher:Emit(nil, "WeaponSkin_System_firingrate",  MNSandbox.SandboxContext()
        :SetData_Number("itemid", m_GunDef.ID)
        :SetData_Number("uin", m_Host:getUin())
    )

    local fireInterval = m_GunDef.FireInterval

    if resultObj:IsExecSuccessed() then
        fireInterval = fireInterval - fireInterval * resultObj:GetData_Number("fvalue")
    end

    return fireInterval
end

-- 攻击时x%弹夹内子弹数量+X
local function getFirinmagazines()
    local resultObj = SandboxLua.eventDispatcher:Emit(nil, "WeaponSkin_System_fightmagazines",  MNSandbox.SandboxContext()
        :SetData_Number("itemid", m_GunDef.ID)
        :SetData_Number("uin", m_Host:getUin())
    )

    if resultObj:IsExecSuccessed() then
        return resultObj:GetData_Number("nvalue")
    end

    return 0
end

local function bulletReload()
    -- 如果当前不能更换弹夹，空气炮需要子弹打完才能更换弹夹
    if not m_Host:getGunLogical():canReload() then
        return false
    end
    
    m_ReloadTime = m_Host:getGunLogical():getReloadTime()
    -- 创造模式直接reload
    if CurWorld:isGodMode() or m_GunDef.NeedBullet == 0 then
        m_ReloadStartMark = CommonUtil:getSystemTick()
        m_Host:getPlayerAnimation():performReloadGun()
        m_Host:getGunLogical():setIsReload(true)
        ActorComponentCallModule(m_Host,"SoundComponent","playSound",m_GunDef.ReloadSound, 1.0, 1+(BlockMaterialUtil:GenRandomFloat() - BlockMaterialUtil:GenRandomFloat()) * 0.4) 
        return true
    else
        local bulletCount = m_Host:getGunLogical():getBulletNum()

        if bulletCount == 0 then
            --如果一颗子弹都没有了
            return false
        else
            -- 不可以补充完整一枪子弹,就全部补充掉 or 可以补充完整一枪子弹
            m_ReloadStartMark = CommonUtil:getSystemTick()
            m_Host:getPlayerAnimation():performReloadGun()
            m_Host:getGunLogical():setIsReload(true)
            ActorComponentCallModule(m_Host,"SoundComponent","playSound",m_GunDef.ReloadSound, 1.0, 1+(BlockMaterialUtil:GenRandomFloat() - BlockMaterialUtil:GenRandomFloat()) * 0.4)
            return true
        end
    end

    return false
end


script.Event.OnInitState = function(context)
    local host = context:GetParamData()
    print("@@@###$$$: lua GunUse State init")
    m_Host = host
    local cameraFov = 75
    g_tblDefaultSet[0].fov = 75
    g_tblDefaultSet[1].fov = 75
    g_tblDefaultSet[2].fov = cameraFov
    g_tblDefaultSet[3].fov = cameraFov

    local resultObj = SandboxLua.eventDispatcher:Emit(nil, "gunTestData",   MNSandbox.SandboxContext()
--[[    --第一人称
    :SetData_Number("RecoilSpeedFirst", 0.06)-- 后坐力速度 (第一人称为上移弧度)
    :SetData_Number("m_MaxRecoilFirst", 30)-- 最大后坐力速度 (第一人称为上移弧度)
    :SetData_Number("RecoilRecoverySpeedFirst", 0.1)-- 后坐力恢复速度 (第一人称为恢复弧度每秒) 以上3个参数区分第一和第三人称。

    :SetData_Number("righthandoffsetxFirst", 0) :SetData_Number("righthandoffsetyFirst", 5) :SetData_Number("righthandoffsetzFirst", -2)-- 右手极向量偏移
    :SetData_Number("rightPoleRelativeToEffectorxFirst", 0.1) :SetData_Number("rightPoleRelativeToEffectoryFirst", 0.5) :SetData_Number("rightPoleRelativeToEffectorzFirst", -0.1)-- 右手偏移相对位置（相对于枪口）
    :SetData_Number("fRightWeightFirst", 0.5)-- 右手权重
    :SetData_Bool("bRightStretchEnableFirst", true)-- 是否右手可拉伸
    :SetData_Number("fRightStretchStartRatioFirst", 0.9)-- 右手拉伸开始比例
    :SetData_Number("fRightStretchMaxRatioFirst", 1.05)-- 右手拉伸最大比例
    :SetData_Number("fRightSecondaryAxisWeightFirst", 0.05)-- 右手次要轴权重

    :SetData_Number("lefthandoffsetxFirst", 0) :SetData_Number("lefthandoffsetyFirst", 5) :SetData_Number("lefthandoffsetzFirst", -2)-- 左手极向量偏移
    :SetData_Number("leftPoleRelativeToEffectorxFirst", 0.1) :SetData_Number("leftPoleRelativeToEffectoryFirst", 0.5 ) :SetData_Number("leftPoleRelativeToEffectorzFirst", -0.1)-- 左手偏移相对位置（相对于枪口）
    :SetData_Number("fLeftWeightFirst", 0.5)-- 左手权重
    :SetData_Bool("bLeftStretchEnableFirst", true)-- 是否左手可拉伸
    :SetData_Number("fLeftStretchStartRatioFirst", 0.9)-- 左手拉伸开始比例   
    :SetData_Number("fLeftStretchMaxRatioFirst", 1.05)-- 左手拉伸最大比例
    :SetData_Number("fLeftSecondaryAxisWeightFirst", 0.05)-- 左手次要轴权重

    --第三人称
    :SetData_Number("RecoilSpeedThird", 0.03)-- 后坐力速度 (第三人称为上移弧度)
    :SetData_Number("MaxRecoilThird", 100)-- 最大后坐力速度 (第三人称为上移弧度)
    :SetData_Number("RecoilRecoverySpeedThird", 0.2)-- 后坐力恢复速度 (第三人称为恢复弧度每秒) 以上3个参数区分第一和第三人称。

    :SetData_Number("RecoilSpeedThirdIK", 0)-- 后坐力速度 (第三人称为上移弧度)
    :SetData_Number("MaxRecoilThirdIK", 100)-- 最大后坐力速度 (第三人称为上移弧度)

    :SetData_Number("righthandoffsetxThird", 0) :SetData_Number("righthandoffsetyThird", 30) :SetData_Number("righthandoffsetzThird", -12)-- 右手极向量偏移
    :SetData_Number("rightPoleRelativeToEffectorxThird", 0.3) :SetData_Number("rightPoleRelativeToEffectoryThird", 1.5) :SetData_Number("rightPoleRelativeToEffectorzThird", -0.8)-- 右手偏移相对位置（相对于枪口）
    :SetData_Number("fRightWeightThird", 0.6)-- 右手权重
    :SetData_Bool("bRightStretchEnableThird", true)-- 是否右手可拉伸
    :SetData_Number("fRightStretchStartRatioThird", 0.9)-- 右手拉伸开始比例
    :SetData_Number("fRightStretchMaxRatioThird", 1.05)-- 右手拉伸最大比例
    :SetData_Number("fRightSecondaryAxisWeightThird", 0.05)-- 右手次要轴权重

    :SetData_Number("lefthandoffsetxThird", 0) :SetData_Number("lefthandoffsetyThird", 20) :SetData_Number("lefthandoffsetzThird", -10)-- 左手极向量偏移
    :SetData_Number("leftPoleRelativeToEffectorxThird", 0.2) :SetData_Number("leftPoleRelativeToEffectoryThird", 1.0 ) :SetData_Number("leftPoleRelativeToEffectorzThird", -0.5)-- 左手偏移相对位置（相对于枪口）
    :SetData_Number("fLeftWeightThird", 0.6)-- 左手权重
    :SetData_Bool("bLeftStretchEnableThird", true)-- 是否左手可拉伸
    :SetData_Number("fLeftStretchStartRatioThird", 0.9)-- 左手拉伸开始比例   
    :SetData_Number("fLeftStretchMaxRatioThird", 1.05)-- 左手拉伸最大比例
    :SetData_Number("fLeftSecondaryAxisWeightThird", 0.05)-- 左手次要轴权重

    :SetData_Number("handmodelscalex", 0.5)
    :SetData_Number("handmodelscaley", 0.3)
    :SetData_Number("handmodelscalez", 0.5)

    :SetData_Number("handmodeloffsetx", 0)
    :SetData_Number("handmodeloffsety", -8)
    :SetData_Number("handmodeloffsetz", 0)

    :SetData_Number("handmodelrotx", 0)
    :SetData_Number("handmodelroty", 0)
    :SetData_Number("handmodelrotz", 0)
    
    :SetData_Number("cameraFov", cameraFov)
    --]]
--以下是全局参数
    :SetData_Number("AimAssistType", 1)--0没有辅助，1全时辅助，2瞄准时辅助
    :SetData_Bool("IsAimReduce", true)-- 是否准心进入最大瞄准圈后移动减速（即改变灵敏度）

--以下是可以作为枪或子弹配置参数--最好作为全局参数
    :SetData_Number("SlowRaduisMax", 100)-- 进行移动减速开始的最大瞄准圈。普通 IsAimReduce开启才使用
    :SetData_Number("SlowRaduisMaxZoom", 100)-- 进行移动减速开始的最大瞄准圈。打开瞄准镜 IsAimReduce开启才使用
    :SetData_Number("SlowModulusPC", 0.3)-- 降低灵敏度的系数小于1大于0--电脑端 IsAimReduce开启才使用
    :SetData_Number("SlowModulusMobile", 0.3)-- 降低灵敏度的系数小于1大于0--移动端 sAimReduce开启才使用
    :SetData_Number("RaduisMin", 30)-- 辅助瞄准最小瞄准圈。普通
    :SetData_Number("RaduisMinZoom", 30)-- 辅助瞄准最小瞄准圈。打开瞄准镜

    :SetData_Number("AutoAimCameraSpeedPC", 0.5)-- 辅助瞄准时，准心向目标移动的速度--电脑端
    :SetData_Number("AutoAimCameraSpeedMobile", 0.5)-- 辅助瞄准时，准心向目标移动的速度--移动端

--以下是可以作为枪或子弹配置参数
    :SetData_Number("AimMaxRange", 10000)--辅助瞄准最远距离

)
end

script.Event.OnLeave = function(context)
    local host = context:GetParamData()
    print("@@@###$$$: lua GunUse State exit")
    if m_Host and m_Host:getGunLogical() then
        --m_Host:getGunLogical():setIsFire(false)
    end
    m_ReloadStartMark = -1
    m_FireStartMark = -1
    m_PullingStartMark = -1
end

script.Event.OnEnter = function(context)
    print("@@@###$$$: lua GunUse State enter")
    if m_Host == nil then
        return
    end
    m_GunDef = DefMgr:getGunDef(m_Host:getCurToolID())
    if m_GunDef == nil then
        return
    end
    m_CurToolID = m_Host:getCurToolID()
    m_CurShortcut = m_Host:getCurShortcut()

    local inputInfo = m_Host:getInputInfo()
    m_NeedReload = inputInfo.reload

    -- 狙击枪射击间隔时间
    if m_GunDef.ContinuousFire == 2 and m_Host:getGunLogical():getPulledGunId() ~= m_CurToolID then
        m_PullingStartMark = CommonUtil:getSystemTick()
        m_bHavePulled = false
        if m_Host:getGunLogical():getPulledGunId() == -100 then
            m_Host:getGunLogical():setPulledGunId(-1)
        else
            m_Host:getGunLogical():setIsReload(true)
        end 
    end
end

script.Event.OnUpdate = function(context)
    if m_Host:IsCurToolBroken() then
        ShowGameTips(GetS(700007))
        return "ToActionIdle"
    end

    local host = context:GetParamData()
    if m_Host == nil or m_GunDef == nil then
        return ""
    end
    if m_CurToolID ~= m_Host:getCurToolID() or m_CurShortcut ~= m_Host:getCurShortcut() or m_Host:isDead() then
        m_Host:setOperate(0) -- PLAYEROP_NULL
        m_Host:getGunLogical():setIsFire(false)
        --m_Host.m_PCCtrl:ResetKey(VK_LBUTTON)
        if GetClientInfo():isMobile() then
            m_Host:getTouchControl():ResetKey(VK_LBUTTON)
        else
            m_Host:getPCControl():ResetKey(VK_LBUTTON)
        end
        return "ToActionIdle"
    end

    -- Reload 期间不处理射击逻辑
    if m_ReloadStartMark > 0 then
        -- 换弹时间超时后，再执行真实换弹操作
        if m_Host:getGunLogical():canReload() and CommonUtil:getSystemTick() - m_ReloadStartMark > m_ReloadTime then
            if CurWorld:isGodMode() then
                m_Host:getGunLogical():doReload(-1)
            else
                m_Host:doReload(m_GunDef:GetCostItemId(), 0)
            end

            m_Host:getPlayerAnimation():performIdle()
            m_Host:getGunLogical():setIsReload(false)
            m_ReloadStartMark = -1
            return "ToActionIdle"
        end

        return ""
    end

    -- Auto reload
    if m_GunDef.NeedBullet ~= 2 and (m_NeedReload or m_Host:getGunLogical():getMagazine() == 0) then
        m_NeedReload = false
        if bulletReload() then
            return ""
        else
            m_Host:getGunLogical():setZoom(false)
            if m_NeedReload then
                return "ToActionIdle"
            end
        end
    end

    if m_PullingStartMark > 0 then
        if CommonUtil:getSystemTick() - m_PullingStartMark > m_GunDef.ManualDelayTime and not m_bHavePulled then
            m_Host:getPlayerAnimation():performPullingGun()
            ActorComponentCallModule(m_Host,"SoundComponent","playSound", 
                m_GunDef.ManualSound,--DefMgr.getGunDef(m_Host:getCurToolID()).ManualSound,
                1.0,
                1.0 + (BlockMaterialUtil:GenRandomFloat() - BlockMaterialUtil:GenRandomFloat()) * 0.4
            )
            m_Host:getGunLogical():setIsReload(true)
            m_bHavePulled = true
        end

        if CommonUtil:getSystemTick() - m_PullingStartMark > m_GunDef.ManualTime then
            m_ReloadStartMark = -1
            m_Host:getGunLogical():setPulledGunId(m_GunDef.ID)
            m_Host:getPlayerAnimation():performIdle()
            m_Host:getGunLogical():doReload(0)
            return "ToActionIdle"
        end

        return ""
    end

    local inputInfo = m_Host:getInputInfo()
    if inputInfo.leftClick or inputInfo.useAction then
        local usetick = CommonUtil:getSystemTick()
        if usetick - m_FireStartTime > m_GunDef.FireInterval then
            local gunlogic = m_Host:getGunLogical()
            if not gunlogic:canUseGun(m_Host:getCurToolID(), usetick) then
                return ""
            end

            -- 不能连发
            if m_FireStartMark ~= -1 and m_GunDef.ContinuousFire ~= 1 then
                m_Host:getPlayerAnimation():performIdle()
                return "ToActionIdle"
            end

            m_IdleStateReady = false
            m_FireStartMark = CommonUtil:getSystemTick()
            m_FireStartTime = m_FireStartMark
            local result = gunlogic:fireOnce()
            if result == 0 then
                --武器皮肤属性随机添加配置子弹
                local addmagazine = getFirinmagazines()

                local maxmagazine = getMagazines()
                local magazine = 0
                if gunlogic:getMagazine() + addmagazine > maxmagazine then
                    magazine = maxmagazine
                else
                    magazine = gunlogic:getMagazine() + addmagazine
                end
                
                gunlogic:setMagazine(magazine)
                local bulletCount = gunlogic:getBulletNum() -- m_Host:getBackPack():getItemCountInNormalPack(m_GunDef:GetCostItemId())
                SetGunMagazine(magazine,bulletCount)

                m_Host:getPlayerAnimation():performFireGun()
                -- 武器皮肤特效
                SandboxLua.eventDispatcher:Emit(nil, "WeaponSkin_System_PlayFireEffect", MNSandbox.SandboxContext():SetData_Userdata("PlayerControl", "player",  m_Host))
                
                -- 武器皮肤音效
                local resultObj = SandboxLua.eventDispatcher:Emit(nil, "WeaponSkin_System_PlayFireSound", MNSandbox.SandboxContext():SetData_Userdata("PlayerControl", "player",  m_Host))
                if (not resultObj:IsExecuted()) or resultObj:IsFailed() then
                    ActorComponentCallModule(m_Host,"SoundComponent","playSound",m_GunDef.ShootSound, 1.0,1+ (BlockMaterialUtil:GenRandomFloat() - BlockMaterialUtil:GenRandomFloat()) * 0.4) 
                end
            else
                if result == 1 then   -- 1 无子弹  2 不可使用
                    ActorComponentCallModule(m_Host,"SoundComponent","playSound",m_GunDef.EmptyShootSound, 1.0, 1+(BlockMaterialUtil:GenRandomFloat() - BlockMaterialUtil:GenRandomFloat()) * 0.4) 
                    -- 提示无子弹
                    ShowGameTips(GetS(700301))
                end
                
                return "ToActionIdle"
            end

            if m_GunDef.ContinuousFire == 2 then
                m_PullingStartMark = CommonUtil:getSystemTick()
                m_Host:getGunLogical():setPulledGunId(-1)
                m_bHavePulled = false
                return ""
            end
        end
    elseif inputInfo.rightClickDown then
        local def = ItemDefCsv:get(m_Host:getCurToolID())

        if def and def.UseTarget == 8 then -- ITEM_USE_GUN
            if m_Host:getGunLogical() then
                m_Host:getGunLogical():setZoom(not m_Host:getGunLogical():getZoom())
                if GetClientInfo():isMobile() then
                    m_Host:getTouchControl():ResetKey(VK_LBUTTON)
                else
                    m_Host:getPCControl():ResetKey(VK_LBUTTON)
                end
            end
            return ""
        end
    else
        if not m_IdleStateReady and CommonUtil:getSystemTick() - m_FireStartMark > 500 then
            m_IdleStateReady = true
            m_Host:getPlayerAnimation():performIdle()
        end
        if CommonUtil:getSystemTick() - m_FireStartMark > m_GunDef.FireInterval then
            m_Host:getPlayerAnimation():performIdle()
            return "ToActionIdle"
        end
    end

    return ""
end

