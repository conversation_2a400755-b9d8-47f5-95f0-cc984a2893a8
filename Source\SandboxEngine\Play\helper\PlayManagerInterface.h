#ifndef __PLAY_MANAGER_INTERFACE_H__
#define __PLAY_MANAGER_INTERFACE_H__

#include "SandboxEngine.h"
#include "BaseClass/SharePtr.h"
#include "Graphics/Texture2D.h"
#include "world_types.h"
#include "WorldSave_generated.h"
#include "CoreCommonDef.h"
#include "chunkrandom.h"
#include "blocks/container.h"
#include "proto_common.pb.h"
#include "Math/Color.h"
#include "ui_texture.h"
#include "GLoader.h"
#include "BlockMeshVert.h"
#include "OgreBezierCurve.h"
#include "LegacyOgreSharedCommon.h"
#include "minisystem/base/IPluginBase.h"
#include "Math/Vector.h"
#include "Mgr/BuildManager/BuildFillParamter.h"
#include "IModPackMgr.h"
#if BUILD_MINI_EDITOR_APP
#include "worldData/SharedSectionData.h"
class EditorBluePrint;
#endif
namespace Rainbow
{
	class Model;
}

//tolua_begin
enum MapRefreshType  //用32位数据来表示数据改变的类型，目前预计是够了。
{
	MRT_Birth = 1 << 0,
	MRT_Death = 1 << 1,
	MRT_StarStation = 1 << 2,
	MRT_Totem = 1 << 3,
	MRT_Portal = 1 << 4,
	MRT_Boss = 1 << 5,
	MRT_SelfTransform = 1 << 6,
	MRT_Revive = 1 << 7,
	MRT_Death_Jar = 1 << 8,
};
//tolua_end
enum BlockCheckType
{
	STILL_FLUID_MATERIAL,
	DOOR_MATERIAL,			//DoorMaterial
	BLOCK_BOMB,					// BlockBomb
	MONSTER_STATUE_SPECIAL,	//BlockMonsterStatueSpecial
	BLOCK_CHEST_MATER,		//ChestMaterial
	BLOCK_CANVAS_MATER, 	//BLOCK_CANVAS
};

enum ContainerCheckType
{
	CONTAINER_FURNACE,			//FurnaceContainer
	CONTAINER_WORLD_FUNNEL,		//WorldFunnelContainer
	CONTAINER_WORLD_EMITTER,	//WorldEmitterContainer
	CONTAINER_WORLD_SIGNES,		//WorldSignsContainer
};

class World;
class WorldManager;
/* ItemIconManager 基类 */
class EXPORT_SANDBOXENGINE ItemIconManagerInterface
{
public:
	ItemIconManagerInterface();
	virtual ~ItemIconManagerInterface() {}

	virtual void CompeleteTask(int blockid, Rainbow::SharePtr<Rainbow::Texture2D> tex) = 0;
	virtual void AddGenTask(int blockid, int asyncSeq) = 0;
	virtual void resetItemIcon(int itemid) = 0;
	virtual Rainbow::SharePtr <Rainbow::Texture2D> getItemIcon(int itemid, int& u, int& v, int& width, int& height, int& r, int& g, int& b) = 0;
	virtual Rainbow::SharePtr<Rainbow::Texture2D> getIconTexture(int itemid, Rainbow::RectInt& rect, Rainbow::ColorRGBA32& color) = 0;
	virtual Rainbow::SharePtr<Rainbow::Texture2D> getDoubleWeaponIconTexture(int itemid, Rainbow::RectInt& rect, Rainbow::ColorRGBA32& color, fairygui::GLoader& pLoader) = 0;
	virtual Rainbow::SharePtr<Rainbow::Texture2D> getDoubleWeaponIconTexture(int itemid, Rainbow::UILib::UITexture& pTexture) = 0;
	virtual Rainbow::SharePtr<Rainbow::Texture2D> getAvatarHeadInfo(const char* avatarInfo, const char* saveId) = 0;


};

class SkillResult;
class SkillPlayerInfo;
/* SkillManager 基类 */
class EXPORT_SANDBOXENGINE SkillManagerInterface
{
public:
	SkillManagerInterface() {};
	virtual ~SkillManagerInterface() {}

	virtual void init() = 0;
	virtual void release() = 0;
	virtual const SkillResult* parseSkillFile(const char* file) = 0;
	virtual const SkillResult* getSkill(const char* file) = 0;
	virtual const SkillPlayerInfo* getSkillPlayerInfo(WORLD_ID uin) = 0;
	virtual void removeSkillPlayerInfo(WORLD_ID uin) = 0;
	virtual void update(float elpase) = 0;
};

/* BTManager 基类 */
class EXPORT_SANDBOXENGINE BTManagerInterface
{
public:
	BTManagerInterface() {};
	virtual ~BTManagerInterface() {}
	virtual void Tick() = 0;
	virtual void Reload() = 0;
};
class WorldContainer;
/* StarStationTransferMgr 基类 */
class EXPORT_SANDBOXENGINE StarStationTransferMgrInterface
{
public:
	StarStationTransferMgrInterface();
	virtual ~StarStationTransferMgrInterface() {}
	virtual bool hasStarStationInCurMap(int mapid) = 0;
	virtual void leaveWorld() = 0;
	virtual void clearTransferData() = 0;
	virtual bool loadMapStarStationData(long long owid, int specialType = 0) = 0;
	virtual bool writeToFile(bool isLeaveWorld) = 0;

	virtual void SetActive(WorldContainer* container, bool isActive) = 0;

	virtual bool loadMapStarStationDataFlatBuff(void* buf, int buflen) = 0;
};

struct DesertTradeCaravanMessage
{
	float saveTime; //保存时间
	WCoord explorePos;
	WCoord workPos;
	int objId;
	bool alive;
	DesertTradeCaravanMessage(WCoord pos, float time, bool calive = false) :explorePos(pos), saveTime(time), alive(calive) {};
};

/* DesertTradeCaravanMgr 基类 */
class EXPORT_SANDBOXENGINE DesertTradeCaravanMgrInterface
{
public:
	DesertTradeCaravanMgrInterface();
	virtual ~DesertTradeCaravanMgrInterface() {}
	virtual void tick() = 0;
	virtual void leaveWorld() = 0;
	virtual bool saveFile() = 0;
	virtual bool checkGenVillage() = 0;
	virtual void setShouldExplore(bool explore) = 0;
	virtual DesertTradeCaravanMessage* findVillageCaravan(WCoord explorePos, bool compExplore = true) = 0;
};

/* ThermalSpringMgr 基类 */
class EXPORT_SANDBOXENGINE ThermalSpringMgrInterface
{
public:
	ThermalSpringMgrInterface();
	virtual ~ThermalSpringMgrInterface() {}
	virtual void leaveWorld() = 0;
};

/* EnemyContextMgr 基类 */
class EXPORT_SANDBOXENGINE EnemyContextMgrInterface
{
public:
	EnemyContextMgrInterface();
	virtual ~EnemyContextMgrInterface() {}
	virtual void tryCreateBoss() = 0;
	virtual void setWorldAnManager(World* InWorld, WorldManager* InWorldManager) = 0;
};

//tolua_begin
struct BluePrintMaterial
{
	int itemid;
	int num;
	int durable;
	int enchantnum;
	int enchants[MAX_ITEM_ENCHANTS];

	GridRuneData runedata;
	int getIthEnchant(int i)
	{
		assert(i >= 0 && i < enchantnum);
		return enchants[i];
	}
};
//tolua_end
namespace Rainbow
{
	class IActorBody;
}
/* BluePrintMgr 基类 */
class EXPORT_SANDBOXENGINE BluePrintMgrInterface : public IPluginBase
{
public:
	BluePrintMgrInterface();
	virtual ~BluePrintMgrInterface() {}
	virtual void saveWaitLoadBluePrintAreas(flatbuffers::FlatBufferBuilder& builder, std::vector<flatbuffers::Offset<FBSave::BluePrintData>>* blueprints) = 0;
	virtual void addBluePrintArea(std::string skey, const WCoord& start, const WCoord& dim) = 0;

	virtual Rainbow::IActorBody* CreateActorBody(std::vector<WCoord>& vpos, std::vector<unsigned int>& vcolor) = 0;
	virtual bool pileIsMonsterStatueSpecialBlockId(int blockResID) = 0;
	virtual bool pileIsNormalBlock(int blockResID) = 0;
	virtual bool pileIsSpecialBlockId(int blockid) = 0;

	virtual bool IsBlockPileSpecial(int blockResID, int& rangex, int& rangey, int& rangez) = 0;
	virtual bool IsBlockDummy(int blockResID, bool& isbottom, int blockdata) = 0;
	virtual bool IsBlockManualEmitter(int blockResID, bool& getCore, World* pworld, const WCoord& blockpos, int blockdata, WCoord& pos) = 0;
};

struct GenTerrResult;
/* PixelMapMgr 基类 */
class EXPORT_SANDBOXENGINE PixelMapMgrInterface
{
public:
	PixelMapMgrInterface();
	virtual ~PixelMapMgrInterface() {}
	virtual void OnTick() = 0;
	virtual void OnChunkChange(int pChunkX, int pChunkZ) = 0;
	virtual void OnChunkChange(Chunk *chunk) = 0;
	virtual void OnBlockChange(int pX, int pY, int pZ) = 0;

	virtual void PushGenTerrResult(GenTerrResult& Result) = 0;

	virtual Rainbow::SharePtr<Rainbow::Texture2D> GetCurrentPixelMapTexture2D(int pLeftX, int pBottomY, int pWidth, int pHeight) = 0;
};

/* MapInfoRefreshCenter 基类 */
class EXPORT_SANDBOXENGINE MapInfoRefreshCenterInterface
{
public:
	MapInfoRefreshCenterInterface() {}
	virtual ~MapInfoRefreshCenterInterface() {}

	virtual void OnTick() = 0;
	virtual void Reset() = 0;
};

/* ActorGeniusMgr 基类 */
class EXPORT_SANDBOXENGINE ActorGeniusMgrInterface : public IPluginBase
{
public:
	ActorGeniusMgrInterface();
	virtual ~ActorGeniusMgrInterface() {}
	virtual int getRoleModelID(int skinid = 0) = 0;
	virtual bool isOldRoleSkin(int skinid) = 0;
};

/* AvatarEffectMgr 基类 */
class EXPORT_SANDBOXENGINE AvatarEffectMgrInterface
{
public:
	AvatarEffectMgrInterface();
	virtual ~AvatarEffectMgrInterface() {}
	virtual void OnTick(World* m_world) = 0;
};

namespace Rainbow
{
	class GameScene;
}
/* MapMarkingMgr 基类 */
class EXPORT_SANDBOXENGINE MapMarkingMgrInterface
{
public:
	MapMarkingMgrInterface();
	virtual ~MapMarkingMgrInterface() {}

	virtual void AttachToScene(Rainbow::GameScene* scene) = 0;
	virtual void DetachFromScene(Rainbow::GameScene* scene) = 0;
	virtual bool IsInScene() const = 0;
	virtual Rainbow::GameScene* GetScene() const = 0;
};

/* SummonMonsterSiegeMgr 基类 */
class EXPORT_SANDBOXENGINE SummonMonsterSiegeMgrInterface
{
public:
	SummonMonsterSiegeMgrInterface() {}
	virtual ~SummonMonsterSiegeMgrInterface() {}

	virtual void OnUpdate(float dtime) = 0;
	virtual void OnTick() = 0;
};

/* VehicleMgr 基类 */
class EXPORT_SANDBOXENGINE VehicleMgrInterface : public IPluginBase
{
public:
	VehicleMgrInterface();
	virtual ~VehicleMgrInterface() {}

	virtual Rainbow::Model* getItemModel(std::string userdatastr, ITEM_MESH_TYPE meshtype) = 0;
};

/* AntiCheatMgr 基类 */
class EXPORT_SANDBOXENGINE AntiCheatMgrInterface : public IPluginBase
{
public:
	AntiCheatMgrInterface();
	virtual ~AntiCheatMgrInterface() {}
	
	virtual bool CheckLimit(int uin, int pb_code, int size) = 0;
	virtual void RecordProtocolMax(int uin, int pb_code) = 0;
	virtual bool CheckMove(int msgCode) = 0;
	virtual bool IsCheckMultiLogin() = 0;
	virtual void SetInEvent(const std::string& event) = 0;
	virtual void SetOutEvent() = 0;
	virtual void RecordPlaceBlock(int block_id, int data, int world_id, int x, int y, int z) = 0;
	virtual void setInProtocol(int protocol, int sender) = 0;
	virtual void outProtocol() = 0;
};

#ifdef DEDICATED_SERVER
#include "proto_pb/proto_gs2ds.pb.h"
#endif
/* ZmqMgr 基类 */
class EXPORT_SANDBOXENGINE ZmqMgrInterface
{
public:
	ZmqMgrInterface();
	virtual ~ZmqMgrInterface() {}
	virtual void Tick() = 0;
	virtual void CloseRoom(int delay, const std::string& msg) = 0;
	virtual void PubNsqMsg(const char* topic, long long owid, const void* data, const int datalen) = 0;
	virtual bool IsNeedSaveChunk() = 0;
	virtual int SaveChunkToDataServer(int uin, long long owid, int mapid, int x, int z, const void* data, int datalen) = 0;
	virtual int SaveActorToDataServer(int uin, long long owid, int mapid, int x, int z, const void* data, int datalen) = 0;
	virtual int LoadChunkFromDataServer(int uin, long long owid, int mapid, int x, int z, bool loadactor = false) = 0;
	virtual const char* GetUinroomid() = 0;
	virtual int GetInitokTime() = 0;
	virtual const char* GetPushServerAddr() = 0;
#ifdef DEDICATED_SERVER
	virtual int SaveDataToRedisDataServer(miniw::RTRedis type, const void* data, int datalen, long long owid, int uin) = 0;
#endif
	virtual void OnPlayerConnected(int uin, int max) = 0;
	virtual std::map<int, std::set<int> > GetStudioTeamRobot() = 0;

	virtual int GetPersonalRoomTimeoutMaxSec() = 0;
	virtual int GetPersonalRoomTimeoutKeepSec() = 0;
	virtual int GetPersonalRoomTimeoutFreeSec2() = 0;
	virtual std::vector<char*>& GetRentnodeMultiAddr() = 0;
	virtual std::string& GetMultiOutAddr() = 0;
	virtual int SendUserMsgToServers(const std::vector<std::string>& roomids, const std::string& msg) = 0;
	virtual MNSandbox::Notify<std::string, std::string>& GetNotifyRecvUserMsg() = 0;
};

namespace MNSandbox
{
	class GameObject;
}

//tolua_begin
enum DHS_OP_ERR
{
	OP_SUCCESS = 0,  //成功
	OP_KEY_DATA_LEN = 1,  //key/数据太大
	OP_CD_LMT = 2, // CD超限
	OP_QPM_LMT = 3, // QPS/QPM超限
	CONTAIN_INVALID_CHAR = 4, // 包含非法字符
	KEY_IS_NULLPTR = 5, // key为空指针
	OP_KEY_FAIELD_CANT_SET = 6, // 失败的key禁止写入
};
//tolua_end
#ifdef DEDICATED_SERVER
#include "type/TypeCommon.h"
#endif
/* DataHubService 基类 */
class EXPORT_SANDBOXENGINE DataHubServiceInterface
{
public:
	DataHubServiceInterface();
	virtual ~DataHubServiceInterface() {}
	virtual MNSandbox::GameObject* GetDataStoreObject(const char* name, const char* scope = nullptr) = 0;
	virtual MNSandbox::GameObject* GetOrderedDataStoreObject(const char* name, const char* scope = nullptr) = 0;
#ifdef DEDICATED_SERVER
	virtual MNSandbox::Notify<miniw::RTGaussRedis, miniw::FailCode, std::string, std::string, std::string>& GetNotifyDataStoreResp() = 0;
	virtual MNSandbox::Notify<miniw::RTGaussRedis, miniw::FailCode, std::string, MNSandbox::vecKVData>& GetNotifyDataStoreVecResp() = 0;
#endif
	virtual int RemoveDataStore(const char* name, const char* scope = nullptr) = 0;
};

namespace MINIW
{
	struct obj_scene_data;
}
/* UgcAssetMgr 基类 */
class EXPORT_SANDBOXENGINE UgcAssetMgrInterface
{
public:
	UgcAssetMgrInterface();
	virtual ~UgcAssetMgrInterface() {}
	
	//获取obj模型的数据
	virtual bool GetObjSceneData(const std::string& assetId, MINIW::obj_scene_data& osd, std::vector< Rainbow::SharePtr<Rainbow::Texture2D>>& textures, std::vector<int>& indices) = 0;
	//获取资源的路径
	virtual std::string GetAssetFilePath(const std::string& assetId) = 0;
	//加载音频资源
	virtual Rainbow::AutoRefPtr<Rainbow::DataStream> LoadAudioAsset(const std::string& assetId) = 0;
	virtual void LoadAudioAssetAsync(const std::string& assetId, const std::function<void(Rainbow::AutoRefPtr<Rainbow::DataStream>)>& loadFinish) = 0;
};

struct TerrainSpecialData;
class TGenUnitSpecialSubOrderBiomeRandomFill;
struct SaveSingleBuildData;
namespace CityBuildConfig
{
	struct CityBiomeSpecialSubOrderData;
	struct CityData;
	struct SingleBuildConfig;
	struct SingleBuildData;
}
/* CityConfig 基类 */
class EXPORT_SANDBOXENGINE CityConfigInterface
{
public:
	CityConfigInterface();
	virtual ~CityConfigInterface();
	virtual bool useNewBaseBuild() = 0;
	virtual WCoord getBaseBottomLeftByPos(WCoord pos) = 0;
	virtual bool isInBaseLoadChunk(const ChunkIndex& index) = 0;
	virtual bool isSpecialBiome(int biome) = 0;

	virtual bool GetNeedGenStarStation() = 0;
	virtual bool GetNeedGenGiantFloatingIsland() = 0;
	virtual bool GetNeedGenDungeons() = 0;
	virtual bool GetNeedGenRookieHouse() = 0;
	virtual int GetBaseToCityRange() = 0;
	virtual int GetToCityRange() = 0;
	virtual int GetBaseHeightScore() = 0;
	virtual int GetBaseMaxHeightDiff() = 0;
	virtual int GetBaseAimScore() = 0;
	virtual Rainbow::Vector3i GetBaseDataSize() = 0;
	virtual Rainbow::Vector2i getMaxBiomeSizeCity() = 0;
	virtual const CityBuildConfig::SingleBuildData* GetSingleBuildData() = 0;
	virtual int GetBossBuildRange() = 0;
	virtual const CityBuildConfig::CityBiomeSpecialSubOrderData* getSubOrderDataByReplaceBiome(int biomeid) = 0;
	virtual int getCityIndex(int biomeid, int index) = 0;
	virtual const CityBuildConfig::CityDataConfig* getCityDataByIndex(int index) = 0;
	virtual const CityBuildConfig::SingleBuildConfig& getSingleBuildConfig() = 0;
	virtual const CityBuildConfig::SingleBuildData* getSingleBuildDataByIndex(int index) = 0;
	virtual const CityBuildConfig::SingleBuildData* getSingleBuildDataByName(const std::string& name) = 0;
	virtual int GetMaxCityCount() = 0;
	virtual int GetCityProtectedRange() = 0;
	virtual int GetSOCSmallBuildMaxSize() = 0;

	virtual void UpdateCityBiomeTerrainID(TGenUnitSpecialSubOrderBiomeRandomFill* genFill, std::vector<int>& retbuf, TerrainSpecialData* data, int parentXLen, int parentZLen, int parentOx, int parentOz, int parentRangeX, int parentRangeZ,
		std::function<void(int, int, int, std::vector<int>&)> const& setNewBiome,
		std::function<int(int, int)>const & getNewBiome,
		std::function<int(int, int)>const & getOldBiome,
		std::function<void(int, int, int)>const & setOldBiome) = 0;
};

class WorldMapData;
#include "Mgr/CityManager/CityData.h"
/* CityMgr 基类 */
class EXPORT_SANDBOXENGINE CityMgrInterface //tolua_exports
{//tolua_exports
public:
	CityMgrInterface() {};
	CityMgrInterface(World* world) {};
	virtual ~CityMgrInterface() {}

	virtual bool addNewCity(const ::CityData& data) = 0;
	virtual void tick() = 0;
	virtual void buildBase(WCoord playerPos) = 0;
	//强加载区域
	virtual void loadForceChunk() = 0;
	virtual bool loadCityFile(long long owid, long long uin, int mapId, int specialType/* = NORMAL_WORLD*/) = 0;
	virtual bool saveFile(bool force = false) = 0;
	//处理找到的城市数据, 如果是本机,直接处理,如果是客机, 则要发送事件
	virtual void checkFindCity(const std::vector<ChunkSpecialBiomeData>& specialBiome, WORLD_ID uin) = 0;

	//tolua_begin
	//处理请求
	virtual bool findNearCity(long long uin, WCoord pos, int range) = 0;
	virtual  WCoord genCityPrecious(long long uin) = 0;
	virtual const std::vector<CityData>& GetAllCityData() = 0;
	virtual const std::map<std::string, SaveSingleBuildData>& GetAllSingleBuildData() = 0;
	virtual void onAddSingleBuild(const std::string& buidlName, int x, int z, int rangeX, int rangeZ, int bRangeX, int bRangeZ) = 0;
	virtual bool checkCanAddSingleBuild(const std::string& name) = 0;
	virtual bool checkPosCanPlaceCity(ChunkIndex chunkIndex, int rangeX, int rangeZ, std::vector<int> canPlaceBiomeList = std::vector<int>(), bool checkOtherCity = true, int dir = -1) = 0;
	//tolua_end
	/**
	 * 判断给定坐标点是否在保护区内
	 * @param position 待检查的坐标点
	 * @return true: 在保护区内 false: 不在保护区内
	 * @note 保护区定义为CityData的范围向外扩展20格，及SaveSingleBuildData向外扩展20格
	 */
	virtual int isInProtectedArea(const WCoord& position) = 0;
	virtual bool isInProtectedChunkArea(const ChunkIndex& cidx) = 0;
	virtual int isInSafeArea(const WCoord& position, WCoord& safeMin, WCoord& safeMax) = 0;
	virtual void onCityGenEnd() = 0;
	virtual void onBuildEnd(World* world, const BuildData& data) = 0;
	virtual void onCityBuildAllEnd(World* world) = 0;
	virtual void onSocMapCreateEndProcessing(World* world) = 0;
};//tolua_exports

struct BuildData;
/* BuildMgr 基类 */
class EXPORT_SANDBOXENGINE BuildMgrInterface
{
public:
	BuildMgrInterface() {};
	BuildMgrInterface(World* world) {};
	virtual ~BuildMgrInterface() {}

	virtual void tick() = 0;
	virtual void saveFile() = 0;
	virtual void loadFile() = 0;
	virtual void AddBuild(const BuildData& data, int buildIndex, const std::string& type) = 0;
};

//神像相关参数设置
class EXPORT_SANDBOXENGINE GodStatueParams //tolua_exports
{ //tolua_exports
public:
	//GM命令相关参数接口,方便产品调参//////////////////////////////////////////////
	//神庙生成相关
	static int CREATE_COUNT_PER_STEP;
	static int GEN_BUFFER_PER_STEP;
	static int TICK_COUNT_PER_STEP;
	static int REPLACE_BLOCK_RATE;
	static int CHECK_HEIGHT;//该高度之下才检测边界范围

	static int GOD_TEMPLE_START_FILL_HEIGHT;//神庙起始填充高度

	//神龛
	static int GOD_STATUE_RATE;//神龛中出现神像的几率
	static int BALDACHINE_RATE;//(100/10000)神龛出现的几率
	static int BALDACHINE_LIMIT;//两个神龛之间的最小距离限制 16个trunk
	//GM命令相关参数接口,方便产品调参 end//////////////////////////////////////////////

	//常量
	static const int FILL_FOUNDATION_BLOCK_ID = 422;
	static const int REPLACE_SRC_ID = 422;
	static const int REPLACE_DEST_ID = 503;

	static const int MAX_ROOM_TYPE = 5;
	static const int BIG_ROOM_BLOCK_ID = 678;
	static const int SMALL_ROOM_BLOCK_ID = 671;

	static const int BOSS_POS_ID = 614;
}; //tolua_exports

class IGameMode;
class IClientPlayer;
class IClientActor;
class IActorBoss;
class BlockMaterial;
class Chunk;
class Section;
class MapInfoRefreshCenter;
class BaseItemMesh;
class FullyCustomModel;
class WorldProxy;
class IActorLocoMotion;
class SandboxMgrBase;
class WorldMapData;
class EffectManager;
class BlockScene;
class CameraModel;
class IPlayerControl;
class PathFinderBase;
namespace Rainbow
{
	class Camera;
	class Entity;
}
namespace game
{
	namespace common
	{
		class PB_PlayerBriefInfo;
	}
}
class ObserverEvent;
class WorldStorageBox;
class ErosionStorageBox;
struct GridCopyData;
class GridDataComponent;
enum ActorType
{
	ACTOR_PROJECTILE,			// ClientActorProjectile
	ACTOR_VEHICLE_ASSEMBLE,	//ActorVehicleAssemble
	ACTOR_BOMB,				// ActorBomb
	ACTOR_ROCKET,				// ActorRocket
	ACTOR_SHAPE_SHIFT_HORSE,	// ActorShapeShiftHorse
	ACTOR_BASKETBALL,			// ActorBasketBall
	ACTOR_ITEM,				// ClientItem
	ACTOR_TRAIN_CAR,			// ActorTrainCar
};

enum LocoMotionType
{
	VEHICLE_ASSEMBLE_LOCOMOTION,	// VehicleAssembleLocoMotion
	PHYSICS_LOCOMOTION,			// PhysicsLocoMotion
};
enum TerrType
{
	CHUNK_GEN_EARTH_CORE,
	CHUNK_GEN_NORMAL,
	CHUNK_GEN_PLANET_SPACE,
	GOD_TEMPLE_BUILD,
};
#include "SandboxSceneObject.h"
#include "actors/helper/ActorComponent_Base.h"

/* SandboxActorSubsystem 基类 */
class EXPORT_SANDBOXENGINE ISandboxActorSubsystem
{
public:
	ISandboxActorSubsystem();
	virtual ~ISandboxActorSubsystem() 
	{

	}
	virtual bool Awake() = 0;
	virtual bool Init() = 0;
	virtual bool Shut() = 0;

	virtual IGameMode* CreateGameMode(long long worldid) = 0;
	virtual BaseItemMesh* CreateClientItemModel(int itemid, ITEM_MODELDISP_TYPE disptype, float customscale = 1.0f, int userdata = 0, ITEM_MESH_TYPE meshtype = NORMAL_MESH, std::string userdatastr = "", int layer = -1) = 0;
	virtual MapInfoRefreshCenterInterface* CreateMapInfoRefreshCenter(World* world) = 0;
	virtual BTManagerInterface* CreateBTManager() = 0;
	virtual SummonMonsterSiegeMgrInterface* CreateSummonMonsterSiegeMgr(World* world) = 0;
	virtual SkillManagerInterface* CreateSkillMgr() = 0;
	virtual SandboxMgrBase* CreateSandboxMgr(const std::string& mgrname) = 0;
	virtual Rainbow::IActorBody* CreateNewIActorBody() = 0;
	virtual EffectManager* CreateNewEffectManager(World* world) = 0;
	virtual void HandleEnemyContext(World* world, WorldManager* worldmgr) = 0;
	virtual long long PackObjId(long long id) = 0;
	virtual void GetTriggerPlayer(IClientPlayer*& Player, const WCoord& placepos, const int& dir) = 0;
	virtual Rainbow::IActorBody* CreateCustomModelActorBody(int type) = 0;
	virtual Rainbow::IActorBody* CreateFullyCustomModelActorBody(int type, FullyCustomModel* fcm) = 0;
	virtual const std::string BodyGetSkinName(int slot, int level) = 0;
	virtual const std::string BodyGetSkinEquipName(int slot, int level) = 0;
	virtual int BodySeqType2ID(int seqtype) = 0;   // ActorBody对应动作转换
	virtual bool IsActorType(IClientActor* actor, ActorType type) = 0;
	virtual bool IsLocoMotionType(IActorLocoMotion* loco, LocoMotionType type) = 0;
	virtual void shootIcicleAuto(int itemid, World* pworld, const WCoord& pos, int num) = 0;
	virtual void throwItemByMob(World* pworld, IClientActor* shootactor, float strength, int itemId, int buffId, int randomYaw = 0, int randomPitch = 0, int dropType = 0) = 0;
	virtual IClientActor* throwItemByActorSpecifyTransform(World* pworld, IClientActor* shootactor, float yaw, float pitch, WCoord startPos, float strength, int itemId, bool setfire, bool canpickup, int fireLv = 0, unsigned char attachedEffect = 0, bool damageInsByShooter = true, bool speedInsByStrength = true) = 0;
	virtual long long GetCurObjId() = 0;
	virtual void ResetObjId(long long id = 0x100000000LL) = 0;
	virtual long long GenNextObjId() = 0;
	virtual int  GetCurViewRange(IClientPlayer * player) = 0;
	virtual void SetViewRangeSetting(int range) = 0;
	virtual void HideClientPlayerInfo(IClientPlayer* player) = 0;
	virtual void AddCurActorFrame() = 0;
	virtual int GetPlayerViewInnerRangeOffset() = 0;
	virtual int PlayerCalDropItemCallCount(int rob_enchant_level, float addprob[3]) = 0;
	virtual void GetFinder(PathFinderBase* & finder, IClientActor*pActor, bool m_bCanDestroyblockPath, bool CanPassOpenWoodenDoors, bool CanPassClosedWoodenDoors,
		bool AvoidsWater, bool AvoidsCloud, bool CanSwimming, int mindistance, bool island, std::vector<int>* m_canDestroyBlocklist) = 0;
	virtual Rainbow::Model* LoadActorBodyModel(const char* path, const char* animfile) = 0;
	virtual void ClearMoveForward(IClientActor* ipActor) = 0;
	virtual void TickNavigatePath(IClientActor* ipActor, const WCoord& pos, float  m_Speed) = 0;
	virtual void PathFollowPlay(IClientActor* ipActor, int& bs, int& bh) = 0;
	//ActorProto相关
	virtual void StoreGridData(game::common::PB_ItemData* dest, const BackPackGrid* src, int src_index = -1) = 0;
	virtual void RestorePos(const game::common::PB_Pos& pos, WCoord* blockpos) = 0;
	virtual void StorePos(game::common::PB_Pos* pstPos, const WCoord* blockpos) = 0;

	//BLOCK相关
	virtual void GenerateRainbowGrassMaterial(World* pworld, Section* psection, BlockMaterial* pmtl, Chunk* chunk, int x, int y, int z) = 0;
	virtual void IsBlockType(bool& issame, BlockMaterial* mtl, BlockCheckType type) = 0;
	virtual bool DoorMaterialOpen(int blockdata) = 0;
	virtual void SetBlockSandFallInstantly(bool value) = 0;
	virtual void BigChestMaterialAddRandomItem(const WCoord& curPos, int blockid, WorldProxy* world, const ChunkRandGen& randgen, int npcid = 0) = 0;
	virtual int BlockJarGetInitialData(const WCoord& blockpos, int blockid, WorldProxy* pworld) = 0;
	virtual void RainbowGrassMaterialGenerate(WCoord& pos, int blockid, WorldProxy* pworld, ChunkRandGen& randgen) = 0;
	virtual void BlockSetTemperature(int& tempVal, int resid, int data) = 0;
	virtual void FurnaceOxygenTriggerOxygenJar(World* pworld, const WCoord& blockpos, BlockMaterial* pmtl) = 0;
	virtual void BlockPlayerDestroyBlock(const WCoord& pos, int blockId, WORLD_ID& occupidActor, WCoord& occupidPos, World* world) = 0;
	virtual bool BlockVelocityToAddToEntity(World* pworld, const WCoord& pos, Rainbow::Vector3f& force, int blockid, bool& hasforce, int maxy = -1) = 0;
	virtual bool BlockTryCreatePortal(World* pworld, WCoord& blockpos, int portalid, int width, int height) = 0;
	virtual bool BlockPileExclude(World* pworld, WCoord& blockgrid, BlockMaterial* pmtl, Block& pblock) = 0;
	virtual void ProcessMonsterStatueMaterial(Rainbow::Model* model, BlockMaterial* pmtl) = 0;
	virtual void CubeBlockDestroyedBy(World* pworld, const WCoord& blockpos) = 0;
	virtual void SetWaterCullMode(Rainbow::CullMode cullmode) = 0;  // 流体进行裁剪
	virtual void FluidSetCameraEnable(bool value) = 0;  // 流体进行相机设置
	virtual void FluidSetUnderwaterEnable(bool value) = 0;  // 流体进行水下设置
	virtual void RailAddVertToArray(std::vector<BlockGeomVert>& vertarray, const Rainbow::Vector3f& pos, float u, float v) = 0;
	virtual void RailCalBezierCurveVertex(std::vector<BlockGeomVert>& verts, std::vector<unsigned short>& indices, const MINIW::CatmullRomCurve& bc, const Vector3f& originPos, bool needreset = false, float w1 = BLOCK_FSIZE * 1.5f / 4, float w2 = BLOCK_FSIZE / 6) = 0;
	virtual void RailclearRailBezierCurveData() = 0;
	virtual void BlockAlienTotemEnableSafeArea(const WCoord& blockpos, int blockid, World* world) = 0;
	virtual void BlockAlienTotemCreateSafeArea(World* pworld, const WCoord& center, int radius) = 0;
	virtual void BlockAlienTotemDestroySafeArea(int totemid, World* pworld, const WCoord& center, int radius) = 0;
	virtual void BlockAlienTotemStepUpdateSafeArea(int curtick, World* pworld, const WCoord& center, int radius) = 0;
	// 方块组件事件
	virtual bool OnBlockEvent(const std::string& eEventType, const ObserverEvent* pobevent, int nImmediate = 0) = 0;

	/*
		BlockScriptComponent相关内容
	*/
	virtual MNSandbox::Component* CreateBlockScriptComponent(BlockMaterial* mtl,unsigned int defid) = 0;
	virtual void BSComponentOnUnbindGameObject(MNSandbox::Component* cmp, BlockMaterial* mtl) = 0;
	virtual void BSComponentUpdate(MNSandbox::Component* cmp, unsigned int dt) = 0;
	virtual void BSComponentAddToTickBlocks(MNSandbox::Component* cmp, int mapid, int x, int y, int z, int blockdata) = 0;
	virtual void BSComponentClearTickBlocks(MNSandbox::Component* cmp, int dt) = 0;
	virtual bool BSComponentNeedTick(MNSandbox::Component* cmp) = 0;
	virtual void BSComponentTickBlocks(MNSandbox::Component* cmp, World* pworld) = 0;
	virtual void BSComponentOnChunkEnterWorld(MNSandbox::Component* cmp, const std::vector<Rainbow::Vector3f>& blocks) = 0;
	virtual void BSComponentOnChunkLeaveWorld(MNSandbox::Component* cmp, const std::vector<Rainbow::Vector3f>& blocks) = 0;

	virtual bool BSComponentOnEventFromTrigger(BlockMaterial* mtl, unsigned int eEventType, const ObserverEvent* pobevent, int nImmediate) = 0;
	/*
		判断container类型，或者强制生成new一个
	*/
	virtual void IsContainerTypeOrCreate(WorldContainer*& container, ContainerCheckType type, bool create, const WCoord& pos) = 0;
	virtual void BedLogicHandleOccupy(World* pworld, const WCoord& blockpos) = 0;
	virtual bool BlockCanvasHaveActorSleep(World* pworld, const WCoord& blockpos) = 0;
	virtual WCoord BlockCanvasGetSleepEffectPos(World* pworld, const WCoord& blockpos) = 0;
	virtual WorldContainer* BedLogicHandleGetCoreContainer(World* pworld, const WCoord& blockpos) = 0;
	virtual WCoord BedLogicHandleGetSleepPosition(World* pworld, const WCoord& blockpos) = 0;
	virtual bool FindBlockIn6Direction(World* pworld, int blockid, std::vector<WCoord>& retPos, const WCoord& blockpos, int maxDistance = 64) = 0;
	virtual bool RegionReplicator_GetVertXCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim) = 0;
	virtual bool RegionReplicator_GetVertZCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim) = 0;
	virtual bool RegionReplicator_GetVertYCuboidArray(std::vector<BlockGeomVert>& array1, std::vector<BlockGeomVert>& array2, std::vector<BlockGeomVert>& array3, std::vector<BlockGeomVert>& array4, std::vector<unsigned short>& indices, WCoord startPos, WCoord dim) = 0;
	virtual void InitAllNewBlockFunc() = 0;
	virtual BlockMaterial* CreateNewBlockObject(const char* name, int resid) = 0;

	//创建omod模型
	virtual Rainbow::Model* CreateOmodModel(std::string skey) = 0;
	//创建omod手部模型
	virtual Rainbow::Model* CreateOmodModelHand(std::string skey) = 0;
	//创建obj模型
	virtual Rainbow::Model* CreateObjModel(std::string skey) = 0;
	//创建obj手部模型
	virtual Rainbow::Model* CreateObjModelHand(std::string skey) = 0;
	//生成omod模型icon
	virtual Rainbow::SharePtr<Rainbow::Texture2D> GenOmodModelTexture(std::string skey) = 0;
	//生成obj模型icon
	virtual Rainbow::SharePtr<Rainbow::Texture2D> GenObjModelTexture(std::string skey) = 0;
	//获取obj模型icon
	virtual Rainbow::SharePtr<Rainbow::Texture2D> GetObjModelIcon(std::string skey, int& u, int& v, int& width, int& height, int& r, int& g, int& b) = 0;
	//获取omod模型icon
	virtual Rainbow::SharePtr<Rainbow::Texture2D> GetOmodModelIcon(std::string skey, int& u, int& v, int& width, int& height, int& r, int& g, int& b) = 0;


	virtual void InitSceneObjFactory(MNSandbox::SandboxFactoryNormal<MNSandbox::SandboxNode>* factory) = 0;
	virtual void InitComponentFactory(MNSandbox::SandboxFactoryNormal<ActorComponentBase>* factory) = 0;

	virtual int TouchCtlCheckFlyArea(int x, int y, float scale) = 0;
	virtual int GetAllPhysxItem() = 0;
	//UGC额外添加的接口
	virtual std::string UGCGetInfo() = 0;

	virtual IActorBoss* CreateBossForTerrian(TerrType type, int defid, const WCoord& pos, World* world, int missionflags = 0, WorldMapData* mapdata = nullptr, int bossindex = 0) = 0;

	virtual void PackPlayerAttrib(game::common::PB_PlayerBriefInfo* briefInfo, BRIEF_INFO_SYNC_TYPE type, IClientPlayer* player) = 0;
	virtual void AddPlanarReflectionObject(BlockScene* m_Scene, World* pworld, Rainbow::Camera* m_Camera) = 0;
	virtual void DoExplosion(World* world, IClientActor* pActor, const WCoord& pos, int explosionRadius, bool flaming, bool smoking, int dirmask, int damageType, const  std::string& soundPath, const std::string& effectPath) = 0;
	virtual void CreateExplosionNew(World* world, IClientActor* pActor, const WCoord& pos, int radius, bool upHalf, float atkValue, bool smoking, bool fromSkill) = 0;
	virtual void SetEffectEnable(bool value) = 0;
	virtual void DestroyPlanarReflection() = 0;
	virtual void PlanarReflectionSetWorld(World* world) = 0;

	virtual Rainbow::Entity*  GetUGCEntity(Rainbow::Entity* entity) = 0;
	virtual void BindHandModelForUGCEntity(CameraModel* model) = 0;
	virtual void SetModelAsync(Rainbow::Entity* entity, Rainbow::Model** model, bool isPrefab, float scale) = 0;
	virtual void ModShowWithChunk(WorldContainer* container, bool value) = 0;

	virtual void CreateBirthBuild(WorldManager* worldmgr, World* pworld, IPlayerControl* player, WorldMapData* mapData) = 0;
	virtual Rainbow::Vector2i GetBiomeSizeByCity(const  Rainbow::Vector2i& size) = 0;
	virtual float GetBuildNoise(float x, float z) = 0;
	virtual float BuildNoiseLerp(float a, float b, float x) = 0;
	virtual float BuildNoiseFade(float x) = 0;

	virtual int AddGridData(WorldStorageBox* box, int itemid, GridCopyData& copydata) = 0;
	virtual int AddGridData(ErosionStorageBox* box, int itemid, GridCopyData& copydata) = 0;
	virtual float GetGunDamage(GridDataComponent* comp) = 0;
	virtual GridDataComponent* CreateGridDataComponent(const std::string& type) = 0;
	/*
	 * studio相关解耦逻辑
	*/
#if BUILD_MINI_EDITOR_APP
	virtual void RemoveContainerRailKnotFromNet(WorldContainer* container) = 0;
	virtual SharedSectionData& CreateMechaSectionData(const WCoord& minpos, const WCoord& maxpos) = 0;
	virtual void ReleaseMechaSectionData() = 0;
	virtual EditorBluePrint* CreateEditorBluePrint(const std::string& resKey) = 0;
	virtual bool RemoveEditorBluePrint(std::string& resKey) = 0;
#endif
};
EXPORT_SANDBOXENGINE ItemIconManagerInterface* GetItemIconManagerInterface();

EXPORT_SANDBOXENGINE StarStationTransferMgrInterface* GetStarStationTransferMgrInterface(); 
EXPORT_SANDBOXENGINE DesertTradeCaravanMgrInterface* GetDesertTradeCaravanMgrInterface();
EXPORT_SANDBOXENGINE ThermalSpringMgrInterface* GetThermalSpringMgrInterface();
EXPORT_SANDBOXENGINE EnemyContextMgrInterface* GetEnemyContextMgrInterface();
EXPORT_SANDBOXENGINE BluePrintMgrInterface* GetBluePrintMgrInterface();

EXPORT_SANDBOXENGINE PixelMapMgrInterface* GetPixelMapMgrInterface();
EXPORT_SANDBOXENGINE AvatarEffectMgrInterface* GetAvatarEffectMgrInterface();
EXPORT_SANDBOXENGINE ActorGeniusMgrInterface* GetActorGeniusMgrInterface();
EXPORT_SANDBOXENGINE MapMarkingMgrInterface* GetMapMarkingMgrInterface();
EXPORT_SANDBOXENGINE VehicleMgrInterface* GetVehicleMgrInterface();
EXPORT_SANDBOXENGINE AntiCheatMgrInterface* GetAntiCheatMgrInterface();

EXPORT_SANDBOXENGINE ZmqMgrInterface* GetZmqMgrInterface();
EXPORT_SANDBOXENGINE DataHubServiceInterface* GetDataHubServiceInterface();
EXPORT_SANDBOXENGINE UgcAssetMgrInterface* GetUgcAssetMgrInterface();
EXPORT_SANDBOXENGINE CityConfigInterface* GetCityConfigInterface();
EXPORT_SANDBOXENGINE IModPackMgr* GetIModPackMgr();

EXPORT_SANDBOXENGINE ISandboxActorSubsystem* GetISandboxActorSubsystem();
#endif
